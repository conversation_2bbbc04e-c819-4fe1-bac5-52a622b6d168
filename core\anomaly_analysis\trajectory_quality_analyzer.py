"""
轨迹质量分析器
评估轨迹数据的质量和连续性
"""

from typing import List, Dict, Any
import logging
from datetime import datetime
import numpy as np

from .base_analyzer import BaseAnomalyAnalyzer, AnalysisResult

logger = logging.getLogger(__name__)


class TrajectoryQualityAnalyzer(BaseAnomalyAnalyzer):
    """轨迹质量分析器"""
    
    def __init__(self, config: Dict[str, Any], name: str = None):
        super().__init__(config, name or 'TrajectoryQualityAnalyzer')
        
        # 质量评估阈值
        self.min_segment_duration = config.get('min_segment_duration', 1.0)  # 最小段时长(秒)
        self.max_speed_change = config.get('max_speed_change', 20.0)  # 最大速度变化(km/h)
        self.max_heading_change = config.get('max_heading_change', 90.0)  # 最大航向变化(度)
        self.min_point_count = config.get('min_point_count', 5)  # 最小点数
        self.smoothness_threshold = config.get('smoothness_threshold', 0.8)  # 平滑度阈值
        
        self.logger.info(f"轨迹质量分析器初始化: 最小时长={self.min_segment_duration}s, "
                        f"最小点数={self.min_point_count}")
    
    @property
    def analysis_type(self) -> str:
        return "trajectory_quality"
    
    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        """
        执行轨迹质量分析
        
        Args:
            trajectory_chain: 轨迹链数据
            **kwargs: 其他参数
            
        Returns:
            AnalysisResult: 分析结果
        """
        self.log_analysis_start(trajectory_chain, **kwargs)
        
        if not self.validate_input(trajectory_chain, **kwargs):
            return self.create_result(False, "输入数据验证失败")
        
        try:
            result = self.create_result()
            
            # 分析每个轨迹段的质量
            quality_issues = []
            quality_scores = []
            
            for segment in trajectory_chain:
                segment_analysis = self._analyze_segment_quality(segment)
                
                if segment_analysis['issues']:
                    quality_issues.extend(segment_analysis['issues'])
                
                quality_scores.append(segment_analysis['quality_score'])
            
            # 添加质量问题事件
            for issue in quality_issues:
                result.add_event(issue)
            
            # 计算整体质量统计
            overall_quality = np.mean(quality_scores) if quality_scores else 0.0
            
            result.add_statistic('total_segments', len(trajectory_chain))
            result.add_statistic('quality_issues', len(quality_issues))
            result.add_statistic('overall_quality_score', overall_quality)
            result.add_statistic('segment_quality_scores', quality_scores)
            
            # 按问题类型统计
            issue_types = {}
            for issue in quality_issues:
                issue_type = issue.get('issue_type', 'unknown')
                issue_types[issue_type] = issue_types.get(issue_type, 0) + 1
            result.add_statistic('issue_types', issue_types)
            
            # 添加元数据
            result.add_metadata('analysis_method', 'quality_assessment')
            result.add_metadata('quality_thresholds', {
                'min_segment_duration': self.min_segment_duration,
                'max_speed_change': self.max_speed_change,
                'max_heading_change': self.max_heading_change,
                'min_point_count': self.min_point_count,
                'smoothness_threshold': self.smoothness_threshold
            })
            
            self.log_analysis_complete(result)
            return result
            
        except Exception as e:
            error_msg = f"轨迹质量分析失败: {str(e)}"
            self.logger.error(error_msg)
            return self.create_result(False, error_msg)
    
    def _analyze_segment_quality(self, segment) -> Dict[str, Any]:
        """
        分析单个轨迹段的质量
        
        Args:
            segment: 轨迹段
            
        Returns:
            Dict[str, Any]: 质量分析结果
        """
        issues = []
        quality_factors = {}
        
        # 检查段时长
        duration_score = self._check_segment_duration(segment, issues)
        quality_factors['duration'] = duration_score
        
        # 检查点数量
        point_count_score = self._check_point_count(segment, issues)
        quality_factors['point_count'] = point_count_score
        
        # 检查运动平滑性
        smoothness_score = self._check_motion_smoothness(segment, issues)
        quality_factors['smoothness'] = smoothness_score
        
        # 检查数据完整性
        completeness_score = self._check_data_completeness(segment, issues)
        quality_factors['completeness'] = completeness_score
        
        # 计算综合质量分数
        quality_score = np.mean(list(quality_factors.values()))
        
        return {
            'segment_id': segment.id,
            'quality_score': quality_score,
            'quality_factors': quality_factors,
            'issues': issues
        }
    
    def _check_segment_duration(self, segment, issues: List[Dict]) -> float:
        """检查段时长"""
        duration = segment.duration
        
        if duration < self.min_segment_duration:
            issues.append({
                'type': 'quality_issue',
                'issue_type': 'short_duration',
                'segment_id': segment.id,
                'timestamp': segment.start_time,
                'severity': 'medium',
                'description': f'轨迹段时长过短: {duration:.1f}s < {self.min_segment_duration}s',
                'value': duration,
                'threshold': self.min_segment_duration,
                'analyzer': self.name
            })
            return 0.5
        
        return 1.0
    
    def _check_point_count(self, segment, issues: List[Dict]) -> float:
        """检查点数量"""
        if not hasattr(segment, 'points') or not segment.points:
            issues.append({
                'type': 'quality_issue',
                'issue_type': 'no_points',
                'segment_id': segment.id,
                'timestamp': segment.start_time,
                'severity': 'high',
                'description': '轨迹段无数据点',
                'analyzer': self.name
            })
            return 0.0
        
        point_count = len(segment.points)
        
        if point_count < self.min_point_count:
            issues.append({
                'type': 'quality_issue',
                'issue_type': 'insufficient_points',
                'segment_id': segment.id,
                'timestamp': segment.start_time,
                'severity': 'medium',
                'description': f'轨迹段点数不足: {point_count} < {self.min_point_count}',
                'value': point_count,
                'threshold': self.min_point_count,
                'analyzer': self.name
            })
            return 0.6
        
        return 1.0
    
    def _check_motion_smoothness(self, segment, issues: List[Dict]) -> float:
        """检查运动平滑性"""
        if not hasattr(segment, 'points') or len(segment.points) < 3:
            return 1.0  # 点数不足，无法评估
        
        try:
            # 计算速度和航向变化
            speed_changes = []
            heading_changes = []
            
            for i in range(1, len(segment.points)):
                prev_point = segment.points[i-1]
                curr_point = segment.points[i]
                
                # 速度变化
                prev_speed = self._get_point_speed(prev_point)
                curr_speed = self._get_point_speed(curr_point)
                speed_change = abs(curr_speed - prev_speed)
                speed_changes.append(speed_change)
                
                # 航向变化
                prev_heading = self._get_point_heading(prev_point)
                curr_heading = self._get_point_heading(curr_point)
                heading_change = self._calculate_heading_diff(prev_heading, curr_heading)
                heading_changes.append(heading_change)
            
            # 检查异常变化
            max_speed_change = max(speed_changes) if speed_changes else 0
            max_heading_change = max(heading_changes) if heading_changes else 0
            
            smoothness_score = 1.0
            
            if max_speed_change > self.max_speed_change:
                issues.append({
                    'type': 'quality_issue',
                    'issue_type': 'abrupt_speed_change',
                    'segment_id': segment.id,
                    'timestamp': segment.start_time,
                    'severity': 'medium',
                    'description': f'速度变化过大: {max_speed_change:.1f} km/h',
                    'value': max_speed_change,
                    'threshold': self.max_speed_change,
                    'analyzer': self.name
                })
                smoothness_score *= 0.7
            
            if max_heading_change > self.max_heading_change:
                issues.append({
                    'type': 'quality_issue',
                    'issue_type': 'abrupt_heading_change',
                    'segment_id': segment.id,
                    'timestamp': segment.start_time,
                    'severity': 'medium',
                    'description': f'航向变化过大: {max_heading_change:.1f}°',
                    'value': max_heading_change,
                    'threshold': self.max_heading_change,
                    'analyzer': self.name
                })
                smoothness_score *= 0.7
            
            return smoothness_score
            
        except Exception as e:
            self.logger.warning(f"平滑性检查失败: {e}")
            return 0.8
    
    def _check_data_completeness(self, segment, issues: List[Dict]) -> float:
        """检查数据完整性"""
        if not hasattr(segment, 'points') or not segment.points:
            return 0.0
        
        try:
            missing_fields = []
            total_points = len(segment.points)
            
            # 检查必要字段的完整性
            required_fields = ['lat', 'lon', 'timestamp']
            optional_fields = ['speed', 'heading', 'altitude']
            
            for field in required_fields:
                missing_count = sum(1 for p in segment.points 
                                  if not self._has_valid_field(p, field))
                if missing_count > 0:
                    missing_fields.append(f"{field}({missing_count}/{total_points})")
            
            if missing_fields:
                issues.append({
                    'type': 'quality_issue',
                    'issue_type': 'incomplete_data',
                    'segment_id': segment.id,
                    'timestamp': segment.start_time,
                    'severity': 'high',
                    'description': f'数据字段缺失: {", ".join(missing_fields)}',
                    'missing_fields': missing_fields,
                    'analyzer': self.name
                })
                return 0.5
            
            return 1.0
            
        except Exception as e:
            self.logger.warning(f"数据完整性检查失败: {e}")
            return 0.8
    
    def _has_valid_field(self, point, field: str) -> bool:
        """检查点是否有有效字段"""
        if hasattr(point, field):
            value = getattr(point, field)
        elif isinstance(point, dict):
            value = point.get(field)
        else:
            return False
        
        return value is not None and value != ""
    
    def _get_point_speed(self, point) -> float:
        """获取点的速度"""
        if hasattr(point, 'speed'):
            return point.speed or 0.0
        elif isinstance(point, dict):
            return point.get('speed', 0.0)
        return 0.0
    
    def _get_point_heading(self, point) -> float:
        """获取点的航向"""
        if hasattr(point, 'heading'):
            return point.heading or 0.0
        elif isinstance(point, dict):
            return point.get('heading', 0.0)
        return 0.0
    
    def _calculate_heading_diff(self, heading1: float, heading2: float) -> float:
        """计算航向差异"""
        diff = abs(heading2 - heading1)
        if diff > 180:
            diff = 360 - diff
        return diff
