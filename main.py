#!/usr/bin/env python3
"""
车路协同轨迹匹配工具 - 主程序入口

使用方法:
    python main.py --rtk rtk_data.csv --perception perception_data.csv
    python main.py --config config/custom.json
    python main.py --help
"""

import sys
import os
import argparse
from pathlib import Path

# 添加core模块到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

from core.data_utils import DataLoader
from core.simple_distance_matcher import SimpleDistanceMatcher
from core.output_generator import OutputGenerator


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(
        description="车路协同轨迹匹配工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
    python main.py --rtk data/rtk.csv --perception data/perception.csv
    python main.py --rtk data/rtk.csv --perception data/perception.csv --config config/custom.json
    python main.py --rtk data/rtk.csv --perception data/perception.csv --output output/results/
    python main.py --rtk data/rtk.csv --perception data/perception.csv --verbose
        """
    )
    
    # 必需参数
    parser.add_argument('--rtk', required=True, help='RTK数据文件路径')
    parser.add_argument('--perception', required=True, help='感知数据文件路径')
    
    # 可选参数
    parser.add_argument('--config', default='config/unified_config.json', help='配置文件路径')
    parser.add_argument('--profile', default=None, 
                       choices=['simple_distance'],
                       help='配置文件 (simple_distance: 简单距离评分)')
    parser.add_argument('--output', default='output/results/', help='输出目录')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not Path(args.rtk).exists():
        print(f"错误：RTK数据文件不存在: {args.rtk}")
        sys.exit(1)
        
    if not Path(args.perception).exists():
        print(f"错误：感知数据文件不存在: {args.perception}")
        sys.exit(1)
    
    # 创建输出目录
    Path(args.output).mkdir(parents=True, exist_ok=True)
    
    try:
        # 直接调用core模块中的trajectory_matcher主函数
        if args.verbose:
            print("正在调用轨迹匹配器...")
        
        # 修改sys.argv来传递参数给trajectory_matcher
        import core.trajectory_matcher as tm
        
        # 构建参数列表
        tm_args = [
            '--rtk', args.rtk,
            '--perception', args.perception,
            '--config', args.config,
            '--output-dir', args.output
        ]
        
        # 添加profile参数（如果指定）
        if args.profile:
            tm_args.extend(['--profile', args.profile])
        
        if args.verbose:
            tm_args.append('--verbose')
        
        # 备份原始argv
        original_argv = sys.argv
        
        # 设置新的argv
        sys.argv = ['trajectory_matcher.py'] + tm_args
        
        try:
            # 调用trajectory_matcher的main函数
            tm.main()
            print(f"✅ 匹配完成！")
            print(f"📁 输出目录: {args.output}")
        finally:
            # 恢复原始argv
            sys.argv = original_argv
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 