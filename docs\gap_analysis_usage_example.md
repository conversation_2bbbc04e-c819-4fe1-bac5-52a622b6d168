# 轨迹间隙分析工具使用示例

## 工具概述

`gap_analysis_tool.py` 是一个专门用于分析车路协同感知轨迹匹配结果中间隙问题的工具。它重点关注：

1. **ID轨迹连接处的漏检**：不同ID轨迹段之间的时间间隙
2. **轨迹内部时间跳跃**：同一ID轨迹内部的异常时间间隔
3. **轨迹连续性分析**：整体轨迹的覆盖率和质量评估

## 使用方法

### 基本用法

```bash
python gap_analysis_tool.py --matched-csv 匹配结果.csv --diagnostic-json 诊断结果.json --output 分析结果.json
```

### 参数说明

- `--matched-csv`: 轨迹匹配结果CSV文件路径（必需）
- `--diagnostic-json`: 诊断JSON文件路径（可选，提供额外信息）
- `--config`: 配置文件路径（默认：config.json）
- `--output`: 输出结果文件路径（可选）
- `--verbose`: 详细输出模式

### 实际示例

```bash
# 分析轨迹匹配结果
python gap_analysis_tool.py \
  --matched-csv gap_test_output/rtk_part005_AJ06993PAJ00062D1_trajectory_matched.csv \
  --diagnostic-json gap_test_output/rtk_part005_AJ06993PAJ00062D1_diagnostic.json \
  --output gap_analysis_results.json
```

## 分析结果解读

### 1. 统计概览

```
📊 统计概览:
  • ID连接处间隙数量: 120
  • 轨迹内部时间跳跃数量: 0
  • 分析的轨迹ID数量: 2
```

**解读**：
- 检测到120个ID连接处的间隙（大部分是正常的0.1秒间隔）
- 没有检测到轨迹内部的时间跳跃
- 分析了2个轨迹ID（101835和101987）

### 2. ID连接处间隙详情

#### 正常间隙示例
```
间隙 1:
  • 时长: 0.10秒
  • ID变化: nan → nan
  • 时间段: 2025-02-25T10:04:40.500000+08:00 ~ 2025-02-25T10:04:40.600000+08:00
  • 运动连续性: ✅ 连续
    - 位置跳跃: 0.93m
    - 速度变化: 0.05m/s
    - 航向变化: 0.3°
```

**解读**：
- 0.1秒的正常时间间隔
- 运动连续性良好，位置、速度、航向变化都在合理范围内

#### 异常间隙示例
```
间隙 32:
  • 时长: 0.10秒
  • ID变化: nan → nan
  • 时间段: 2025-02-25T10:05:12.099000+08:00 ~ 2025-02-25T10:05:12.199000+08:00
  • 运动连续性: ❌ 不连续
    - 位置跳跃: 0.00m
    - 速度变化: 0.00m/s
    - 航向变化: 33.4°
```

**解读**：
- 虽然时间间隔正常，但航向变化33.4°超过了阈值（30°）
- 表明可能存在感知数据质量问题

#### 真实ID切换示例
```
间隙 30:
  • 时长: 0.10秒
  • ID变化: nan → 101835.0
  • 时间段: 2025-02-25T10:04:43+08:00 ~ 2025-02-25T10:04:43.100000+08:00
  • 运动连续性: ✅ 连续
```

**解读**：
- 从无匹配（nan）切换到ID 101835
- 运动连续性良好，说明这是正常的ID切换

### 3. 轨迹连续性分析

```
📈 轨迹连续性分析:
  ID 101835.0:
    • 总时长: 28.90秒
    • 数据点数: 290
    • 覆盖率: 100.35%
    • 间隙数量: 0
    • 长间隙数量: 0
    • 平均间隔: 0.100秒

  ID 101987.0:
    • 总时长: 61.30秒
    • 数据点数: 614
    • 覆盖率: 100.16%
    • 间隙数量: 0
    • 长间隙数量: 0
    • 平均间隔: 0.100秒
```

**解读**：
- 两个ID的轨迹都非常连续
- 覆盖率超过100%说明数据频率高于预期
- 平均间隔0.1秒符合10Hz的采样频率
- 没有显著的时间间隙

### 4. 问题诊断

```
🩺 问题诊断:
  ⚠️ 发现 17 个运动不连续的ID连接
```

**解读**：
- 在120个间隙中，有17个存在运动不连续性
- 主要问题是航向变化过大
- 需要进一步检查感知数据质量

## 关键配置参数

### 运动连续性阈值
```json
{
  "switch_dt": 2.0,        // ID切换时间阈值（秒）
  "switch_dist": 10.0,     // 位置跳跃阈值（米）
  "switch_speed": 5.0,     // 速度变化阈值（m/s）
  "switch_heading": 30.0,  // 航向变化阈值（度）
}
```

### 间隙检测阈值
```json
{
  "min_missing_gap": 0.5,  // 最小漏检间隙（秒）
  "max_missing_gap": 5.0,  // 最大漏检间隙（秒）
  "normal_detection_interval": 0.1  // 正常检测间隔（秒）
}
```

## 实际案例分析

### 案例1：正常轨迹
- **特征**：大量0.1秒间隙，运动连续性良好
- **结论**：轨迹质量高，匹配效果好

### 案例2：ID切换
- **特征**：从一个ID切换到另一个ID，运动连续
- **结论**：正常的ID切换，算法正确处理

### 案例3：感知异常
- **特征**：短时间内航向变化过大
- **结论**：感知数据质量问题，需要进一步优化感知算法

## 优化建议

### 1. 针对运动不连续问题
- 检查感知设备的标定精度
- 优化感知算法的平滑性
- 调整轨迹匹配的容差参数

### 2. 针对时间跳跃问题
- 检查时间同步机制
- 优化数据采集频率
- 增强时间戳验证

### 3. 针对低覆盖率问题
- 检查ROI过滤参数
- 优化目标检测算法
- 增强多目标跟踪能力

## 输出文件格式

分析结果保存为JSON格式，包含：

```json
{
  "id_connection_gaps": [
    {
      "type": "id_connection_gap",
      "gap_duration": 0.1,
      "from_id": "101835",
      "to_id": "101987",
      "motion_continuity": {
        "is_continuous": true,
        "distance": 1.5,
        "speed_diff": 0.2,
        "heading_diff": 5.0
      }
    }
  ],
  "internal_time_jumps": [...],
  "trajectory_continuity": {...},
  "statistics": {...}
}
```

## 总结

轨迹间隙分析工具提供了深入的轨迹质量分析能力，能够：

1. **精确识别**：准确识别ID连接处的问题
2. **量化评估**：提供定量的连续性指标
3. **问题诊断**：自动识别常见问题类型
4. **优化指导**：为系统优化提供数据支撑

通过该工具的分析，可以更好地理解轨迹匹配的质量，发现系统中的薄弱环节，并为进一步的优化提供依据。 