"""
结果汇总器
统一处理和汇总所有异常分析结果
"""

from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
from collections import defaultdict

from .base_analyzer import AnalysisResult

logger = logging.getLogger(__name__)


class ResultSummarizer:
    """结果汇总器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化结果汇总器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.enable_detailed_summary = config.get('enable_detailed_summary', True)
        self.enable_statistics = config.get('enable_statistics', True)
        self.enable_recommendations = config.get('enable_recommendations', False)
        
        logger.info("结果汇总器初始化完成")
    
    def summarize(self, results: Dict[str, AnalysisResult]) -> Dict[str, Any]:
        """
        汇总分析结果
        
        Args:
            results: 分析结果字典
            
        Returns:
            Dict[str, Any]: 汇总结果
        """
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_analyzers': len(results),
            'successful_analyzers': sum(1 for r in results.values() if r.success),
            'failed_analyzers': sum(1 for r in results.values() if not r.success),
            'total_events': sum(r.get_event_count() for r in results.values()),
            'analyzer_results': {}
        }
        
        # 汇总每个分析器的结果
        for analyzer_name, result in results.items():
            summary['analyzer_results'][analyzer_name] = self._summarize_single_result(result)
        
        # 生成统计信息
        if self.enable_statistics:
            summary['statistics'] = self._generate_statistics(results)
        
        # 生成详细汇总
        if self.enable_detailed_summary:
            summary['detailed_summary'] = self._generate_detailed_summary(results)
        
        # 生成建议
        if self.enable_recommendations:
            summary['recommendations'] = self._generate_recommendations(results)
        
        # 生成异常事件时间线
        summary['event_timeline'] = self._generate_event_timeline(results)
        
        return summary
    
    def _summarize_single_result(self, result: AnalysisResult) -> Dict[str, Any]:
        """汇总单个分析结果"""
        return {
            'analyzer_name': result.analyzer_name,
            'analysis_type': result.analysis_type,
            'success': result.success,
            'error_message': result.error_message,
            'event_count': result.get_event_count(),
            'statistics': result.statistics,
            'metadata': result.metadata,
            'timestamp': result.timestamp.isoformat()
        }
    
    def _generate_statistics(self, results: Dict[str, AnalysisResult]) -> Dict[str, Any]:
        """生成统计信息"""
        stats = {
            'event_type_counts': defaultdict(int),
            'analyzer_performance': {},
            'severity_distribution': defaultdict(int),
            'time_distribution': defaultdict(int)
        }
        
        # 统计事件类型
        for analyzer_name, result in results.items():
            if result.success:
                for event in result.events:
                    event_type = event.get('type', 'unknown')
                    stats['event_type_counts'][event_type] += 1
                    
                    # 统计严重程度
                    severity = event.get('severity', 'normal')
                    stats['severity_distribution'][severity] += 1
                    
                    # 统计时间分布（按小时）
                    if 'timestamp' in event:
                        try:
                            timestamp = event['timestamp']
                            if isinstance(timestamp, str):
                                timestamp = datetime.fromisoformat(timestamp)
                            hour = timestamp.hour
                            stats['time_distribution'][hour] += 1
                        except Exception:
                            pass
                
                # 分析器性能统计
                stats['analyzer_performance'][analyzer_name] = {
                    'event_count': result.get_event_count(),
                    'success': result.success,
                    'analysis_type': result.analysis_type
                }
        
        # 转换defaultdict为普通dict
        stats['event_type_counts'] = dict(stats['event_type_counts'])
        stats['severity_distribution'] = dict(stats['severity_distribution'])
        stats['time_distribution'] = dict(stats['time_distribution'])
        
        return stats
    
    def _generate_detailed_summary(self, results: Dict[str, AnalysisResult]) -> Dict[str, Any]:
        """生成详细汇总"""
        detailed = {
            'split_analysis': {},
            'id_switch_analysis': {},
            'missing_gap_analysis': {},
            'accuracy_analysis': {},
            'other_analysis': {}
        }
        
        for analyzer_name, result in results.items():
            if not result.success:
                continue
            
            analysis_type = result.analysis_type
            
            if analysis_type == 'split_detection':
                detailed['split_analysis'] = self._summarize_split_analysis(result)
            elif analysis_type == 'id_switch':
                detailed['id_switch_analysis'] = self._summarize_id_switch_analysis(result)
            elif analysis_type == 'missing_gap':
                detailed['missing_gap_analysis'] = self._summarize_missing_gap_analysis(result)
            elif analysis_type == 'accuracy':
                detailed['accuracy_analysis'] = self._summarize_accuracy_analysis(result)
            else:
                detailed['other_analysis'][analyzer_name] = self._summarize_other_analysis(result)
        
        return detailed
    
    def _summarize_split_analysis(self, result: AnalysisResult) -> Dict[str, Any]:
        """汇总分裂分析结果"""
        split_events = result.get_events_by_type('split_confirmed')
        
        summary = {
            'total_splits': len(split_events),
            'split_details': [],
            'affected_ids': set(),
            'total_overlap_duration': 0
        }
        
        for event in split_events:
            summary['split_details'].append({
                'ids': event.get('ids', []),
                'overlap_duration': event.get('overlap_duration', 0),
                'timestamp': event.get('timestamp'),
                'overlap_start': event.get('overlap_start'),
                'overlap_end': event.get('overlap_end')
            })
            
            # 统计受影响的ID
            for id_val in event.get('ids', []):
                summary['affected_ids'].add(id_val)
            
            # 累计重叠时长
            summary['total_overlap_duration'] += event.get('overlap_duration', 0)
        
        summary['affected_ids'] = list(summary['affected_ids'])
        summary['unique_id_count'] = len(summary['affected_ids'])
        
        return summary
    
    def _summarize_id_switch_analysis(self, result: AnalysisResult) -> Dict[str, Any]:
        """汇总ID切换分析结果"""
        switch_events = [e for e in result.events if e.get('type', '').startswith('switch')]
        
        summary = {
            'total_switches': len(switch_events),
            'continuous_switches': 0,
            'discontinuous_switches': 0,
            'switch_details': [],
            'id_pairs': set(),
            'total_gap_duration': 0
        }
        
        for event in switch_events:
            event_type = event.get('type', '')
            if 'continuous' in event_type:
                summary['continuous_switches'] += 1
            elif 'discontinuous' in event_type:
                summary['discontinuous_switches'] += 1
            
            from_id = event.get('from_id')
            to_id = event.get('to_id')
            gap_duration = event.get('gap_duration', 0)
            
            summary['switch_details'].append({
                'from_id': from_id,
                'to_id': to_id,
                'gap_duration': gap_duration,
                'timestamp': event.get('timestamp'),
                'continuity': event.get('continuity_analysis', {})
            })
            
            # 统计ID对
            if from_id and to_id:
                summary['id_pairs'].add((from_id, to_id))
            
            # 累计间隙时长
            summary['total_gap_duration'] += gap_duration
        
        summary['unique_id_pairs'] = len(summary['id_pairs'])
        summary['id_pairs'] = [list(pair) for pair in summary['id_pairs']]
        
        return summary
    
    def _summarize_missing_gap_analysis(self, result: AnalysisResult) -> Dict[str, Any]:
        """汇总漏检分析结果"""
        missing_events = [e for e in result.events if 'missing' in e.get('type', '')]
        
        summary = {
            'total_missing_gaps': len(missing_events),
            'gap_details': [],
            'total_missing_duration': 0,
            'gap_types': defaultdict(int),
            'coverage_ratio': result.statistics.get('rtk_coverage_ratio', 0)
        }
        
        for event in missing_events:
            gap_type = event.get('type', 'unknown')
            duration = event.get('duration', 0)
            
            summary['gap_details'].append({
                'type': gap_type,
                'start_time': event.get('start_time'),
                'end_time': event.get('end_time'),
                'duration': duration,
                'severity': event.get('severity', 'normal')
            })
            
            summary['gap_types'][gap_type] += 1
            summary['total_missing_duration'] += duration
        
        summary['gap_types'] = dict(summary['gap_types'])
        
        return summary

    def _summarize_accuracy_analysis(self, result: AnalysisResult) -> Dict[str, Any]:
        """汇总精度分析结果"""
        try:
            # 获取统计摘要
            statistical_summary = result.metadata.get('statistical_summary', {})
            samples_count = result.metadata.get('samples_count', 0)
            common_time_range = result.metadata.get('common_time_range', {})

            # 基础统计信息
            summary = {
                'total_samples': samples_count,
                'analysis_duration': common_time_range.get('duration', 0),
                'time_range': {
                    'start': common_time_range.get('start_time', ''),
                    'end': common_time_range.get('end_time', ''),
                    'duration_seconds': common_time_range.get('duration', 0)
                }
            }

            # 定位精度汇总
            position_accuracy = statistical_summary.get('position_accuracy', {})
            if position_accuracy:
                summary['position_accuracy'] = {
                    'mean_error_m': position_accuracy.get('mean', 0),
                    'std_error_m': position_accuracy.get('std', 0),
                    'max_error_m': position_accuracy.get('max', 0),
                    'p95_error_m': position_accuracy.get('p95', 0),
                    'p99_error_m': position_accuracy.get('p99', 0)
                }

            # 速度精度汇总
            speed_accuracy = statistical_summary.get('speed_accuracy', {})
            if speed_accuracy:
                abs_acc = speed_accuracy.get('absolute', {})
                rel_acc = speed_accuracy.get('relative', {})
                summary['speed_accuracy'] = {
                    'mean_abs_error_ms': abs_acc.get('mean', 0),
                    'mean_rel_error_percent': rel_acc.get('mean', 0),
                    'max_abs_error_ms': abs_acc.get('max', 0),
                    'p95_abs_error_ms': abs_acc.get('p95', 0)
                }

            # 航向角精度汇总
            heading_accuracy = statistical_summary.get('heading_accuracy', {})
            if heading_accuracy:
                summary['heading_accuracy'] = {
                    'mean_error_deg': heading_accuracy.get('mean', 0),
                    'std_error_deg': heading_accuracy.get('std', 0),
                    'max_error_deg': heading_accuracy.get('max', 0),
                    'p95_error_deg': heading_accuracy.get('p95', 0)
                }

            # 精度等级分布
            accuracy_distribution = statistical_summary.get('accuracy_distribution', {})
            if accuracy_distribution:
                summary['accuracy_distribution'] = {
                    'position_levels': self._extract_accuracy_levels(accuracy_distribution.get('position', {})),
                    'speed_levels': self._extract_accuracy_levels(accuracy_distribution.get('speed', {})),
                    'heading_levels': self._extract_accuracy_levels(accuracy_distribution.get('heading', {}))
                }

            # 异常事件统计
            accuracy_events = result.get_events_by_type(['high_position_error', 'high_speed_error', 'high_heading_error'])
            summary['anomaly_events'] = {
                'total_high_error_events': len(accuracy_events),
                'high_position_errors': len(result.get_events_by_type('high_position_error')),
                'high_speed_errors': len(result.get_events_by_type('high_speed_error')),
                'high_heading_errors': len(result.get_events_by_type('high_heading_error'))
            }

            # 质量评估
            summary['quality_assessment'] = self._assess_accuracy_quality(
                position_accuracy, speed_accuracy, heading_accuracy, accuracy_events
            )

            return summary

        except Exception as e:
            return {
                'error': f"精度分析汇总失败: {e}",
                'total_samples': 0
            }

    def _extract_accuracy_levels(self, level_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取精度等级信息"""
        levels = []
        for key, value in level_data.items():
            if 'level_' in key and isinstance(value, dict):
                levels.append({
                    'threshold': value.get('threshold', 0),
                    'count': value.get('count', 0),
                    'percentage': value.get('percentage', 0)
                })
        return sorted(levels, key=lambda x: x['threshold'])

    def _assess_accuracy_quality(self, position_acc: Dict, speed_acc: Dict,
                               heading_acc: Dict, anomaly_events: List) -> Dict[str, Any]:
        """评估精度质量"""
        try:
            # 定位质量评估
            pos_mean = position_acc.get('mean', float('inf'))
            pos_quality = 'excellent' if pos_mean <= 1.0 else 'good' if pos_mean <= 2.0 else 'fair' if pos_mean <= 5.0 else 'poor'

            # 速度质量评估
            speed_abs = speed_acc.get('absolute', {})
            speed_mean = speed_abs.get('mean', float('inf'))
            speed_quality = 'excellent' if speed_mean <= 1.0 else 'good' if speed_mean <= 2.0 else 'fair' if speed_mean <= 5.0 else 'poor'

            # 航向角质量评估
            heading_mean = heading_acc.get('mean', float('inf'))
            heading_quality = 'excellent' if heading_mean <= 5.0 else 'good' if heading_mean <= 10.0 else 'fair' if heading_mean <= 30.0 else 'poor'

            # 综合质量评估
            quality_scores = {'excellent': 4, 'good': 3, 'fair': 2, 'poor': 1}
            avg_score = (quality_scores.get(pos_quality, 1) +
                        quality_scores.get(speed_quality, 1) +
                        quality_scores.get(heading_quality, 1)) / 3

            overall_quality = 'excellent' if avg_score >= 3.5 else 'good' if avg_score >= 2.5 else 'fair' if avg_score >= 1.5 else 'poor'

            return {
                'overall_quality': overall_quality,
                'position_quality': pos_quality,
                'speed_quality': speed_quality,
                'heading_quality': heading_quality,
                'anomaly_rate': len(anomaly_events) / max(position_acc.get('count', 1), 1) * 100,
                'quality_score': round(avg_score, 2)
            }

        except Exception:
            return {
                'overall_quality': 'unknown',
                'position_quality': 'unknown',
                'speed_quality': 'unknown',
                'heading_quality': 'unknown',
                'anomaly_rate': 0,
                'quality_score': 0
            }

    def _summarize_other_analysis(self, result: AnalysisResult) -> Dict[str, Any]:
        """汇总其他分析结果"""
        return {
            'analyzer_name': result.analyzer_name,
            'analysis_type': result.analysis_type,
            'event_count': result.get_event_count(),
            'events': result.events,
            'statistics': result.statistics
        }
    
    def _generate_event_timeline(self, results: Dict[str, AnalysisResult]) -> List[Dict[str, Any]]:
        """生成事件时间线"""
        timeline = []
        
        for analyzer_name, result in results.items():
            if not result.success:
                continue
            
            for event in result.events:
                timeline_event = {
                    'timestamp': event.get('timestamp'),
                    'analyzer': analyzer_name,
                    'analysis_type': result.analysis_type,
                    'event_type': event.get('type', 'unknown'),
                    'event_data': event
                }
                timeline.append(timeline_event)
        
        # 按时间排序（处理时区问题）
        def safe_timestamp_key(event):
            timestamp = event['timestamp']
            if timestamp is None:
                return datetime.min

            # 如果是字符串，尝试解析
            if isinstance(timestamp, str):
                try:
                    timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                except:
                    return datetime.min

            # 如果有时区信息，转换为naive datetime进行比较
            if hasattr(timestamp, 'tzinfo') and timestamp.tzinfo is not None:
                timestamp = timestamp.replace(tzinfo=None)

            return timestamp

        timeline.sort(key=safe_timestamp_key)
        
        return timeline
    
    def _generate_recommendations(self, results: Dict[str, AnalysisResult]) -> List[Dict[str, Any]]:
        """生成建议"""
        recommendations = []
        
        # 基于分析结果生成建议
        for analyzer_name, result in results.items():
            if not result.success:
                recommendations.append({
                    'type': 'error',
                    'priority': 'high',
                    'message': f"分析器 {analyzer_name} 执行失败: {result.error_message}",
                    'action': '检查分析器配置和输入数据'
                })
                continue
            
            event_count = result.get_event_count()
            
            if result.analysis_type == 'split_detection' and event_count > 0:
                recommendations.append({
                    'type': 'split_detected',
                    'priority': 'medium',
                    'message': f"检测到 {event_count} 个分裂事件",
                    'action': '检查感知算法的目标跟踪稳定性'
                })
            
            elif result.analysis_type == 'id_switch' and event_count > 5:
                recommendations.append({
                    'type': 'frequent_switches',
                    'priority': 'medium',
                    'message': f"检测到频繁的ID切换 ({event_count} 次)",
                    'action': '优化目标关联算法参数'
                })

            elif result.analysis_type == 'accuracy':
                # 精度分析建议
                accuracy_recommendations = self._generate_accuracy_recommendations(result)
                recommendations.extend(accuracy_recommendations)
            
            elif result.analysis_type == 'missing_gap':
                coverage_ratio = result.statistics.get('rtk_coverage_ratio', 1.0)
                if coverage_ratio < 0.8:
                    recommendations.append({
                        'type': 'low_coverage',
                        'priority': 'high',
                        'message': f"RTK覆盖率较低 ({coverage_ratio:.1%})",
                        'action': '检查感知系统的检测能力和参数设置'
                    })
        
        return recommendations

    def _generate_accuracy_recommendations(self, result: AnalysisResult) -> List[Dict[str, Any]]:
        """生成精度分析建议"""
        recommendations = []

        try:
            statistical_summary = result.metadata.get('statistical_summary', {})

            # 定位精度建议
            position_accuracy = statistical_summary.get('position_accuracy', {})
            if position_accuracy:
                mean_error = position_accuracy.get('mean', 0)
                if mean_error > 5.0:
                    recommendations.append({
                        'type': 'poor_position_accuracy',
                        'priority': 'high',
                        'message': f"定位精度较差，平均误差 {mean_error:.2f}m",
                        'action': '检查RTK基站信号质量和感知系统标定'
                    })
                elif mean_error > 2.0:
                    recommendations.append({
                        'type': 'moderate_position_accuracy',
                        'priority': 'medium',
                        'message': f"定位精度中等，平均误差 {mean_error:.2f}m",
                        'action': '优化感知算法或检查传感器标定'
                    })

            # 速度精度建议
            speed_accuracy = statistical_summary.get('speed_accuracy', {})
            if speed_accuracy:
                abs_acc = speed_accuracy.get('absolute', {})
                rel_acc = speed_accuracy.get('relative', {})
                mean_abs_error = abs_acc.get('mean', 0)
                mean_rel_error = rel_acc.get('mean', 0)

                if mean_abs_error > 3.0 or mean_rel_error > 15.0:
                    recommendations.append({
                        'type': 'poor_speed_accuracy',
                        'priority': 'high',
                        'message': f"速度精度较差，绝对误差 {mean_abs_error:.2f}m/s，相对误差 {mean_rel_error:.1f}%",
                        'action': '检查速度估算算法和传感器融合策略'
                    })
                elif mean_abs_error > 1.5 or mean_rel_error > 8.0:
                    recommendations.append({
                        'type': 'moderate_speed_accuracy',
                        'priority': 'medium',
                        'message': f"速度精度中等，绝对误差 {mean_abs_error:.2f}m/s，相对误差 {mean_rel_error:.1f}%",
                        'action': '优化速度滤波算法参数'
                    })

            # 航向角精度建议
            heading_accuracy = statistical_summary.get('heading_accuracy', {})
            if heading_accuracy:
                mean_error = heading_accuracy.get('mean', 0)
                if mean_error > 15.0:
                    recommendations.append({
                        'type': 'poor_heading_accuracy',
                        'priority': 'high',
                        'message': f"航向角精度较差，平均误差 {mean_error:.1f}°",
                        'action': '检查IMU标定和航向角估算算法'
                    })
                elif mean_error > 8.0:
                    recommendations.append({
                        'type': 'moderate_heading_accuracy',
                        'priority': 'medium',
                        'message': f"航向角精度中等，平均误差 {mean_error:.1f}°",
                        'action': '优化航向角滤波和融合算法'
                    })

            # 异常事件建议
            high_error_events = result.get_events_by_type(['high_position_error', 'high_speed_error', 'high_heading_error'])
            if len(high_error_events) > 0:
                total_samples = result.metadata.get('samples_count', 1)
                anomaly_rate = len(high_error_events) / total_samples * 100

                if anomaly_rate > 20.0:
                    recommendations.append({
                        'type': 'high_anomaly_rate',
                        'priority': 'high',
                        'message': f"精度异常率过高 ({anomaly_rate:.1f}%)",
                        'action': '全面检查感知系统和数据质量'
                    })
                elif anomaly_rate > 10.0:
                    recommendations.append({
                        'type': 'moderate_anomaly_rate',
                        'priority': 'medium',
                        'message': f"精度异常率较高 ({anomaly_rate:.1f}%)",
                        'action': '重点检查异常时段的数据质量'
                    })

        except Exception as e:
            recommendations.append({
                'type': 'analysis_error',
                'priority': 'high',
                'message': f"精度分析建议生成失败: {e}",
                'action': '检查精度分析器配置和数据格式'
            })

        return recommendations

    def print_summary(self, summary: Dict[str, Any]):
        """打印汇总结果"""
        print("\n" + "="*80)
        print("🔍 异常分析结果汇总")
        print("="*80)
        
        print(f"\n📊 总体统计:")
        print(f"  • 分析器总数: {summary['total_analyzers']}")
        print(f"  • 成功执行: {summary['successful_analyzers']}")
        print(f"  • 执行失败: {summary['failed_analyzers']}")
        print(f"  • 检测事件总数: {summary['total_events']}")
        
        if 'statistics' in summary:
            stats = summary['statistics']
            print(f"\n📈 事件类型分布:")
            for event_type, count in stats['event_type_counts'].items():
                print(f"  • {event_type}: {count}")
        
        if 'detailed_summary' in summary:
            detailed = summary['detailed_summary']
            
            if detailed['split_analysis']:
                split_info = detailed['split_analysis']
                print(f"\n🔀 分裂分析:")
                print(f"  • 分裂事件数: {split_info['total_splits']}")
                print(f"  • 受影响ID数: {split_info['unique_id_count']}")
                print(f"  • 总重叠时长: {split_info['total_overlap_duration']:.1f}秒")
            
            if detailed['id_switch_analysis']:
                switch_info = detailed['id_switch_analysis']
                print(f"\n🔄 ID切换分析:")
                print(f"  • 切换事件数: {switch_info['total_switches']}")
                print(f"  • 连续切换: {switch_info['continuous_switches']}")
                print(f"  • 不连续切换: {switch_info['discontinuous_switches']}")
                print(f"  • 总间隙时长: {switch_info['total_gap_duration']:.1f}秒")
            
            if detailed['missing_gap_analysis']:
                gap_info = detailed['missing_gap_analysis']
                print(f"\n❌ 漏检分析:")
                print(f"  • 漏检间隙数: {gap_info['total_missing_gaps']}")
                print(f"  • 总漏检时长: {gap_info['total_missing_duration']:.1f}秒")
                print(f"  • RTK覆盖率: {gap_info['coverage_ratio']:.1%}")
        
        if 'recommendations' in summary and summary['recommendations']:
            print(f"\n💡 建议:")
            for rec in summary['recommendations']:
                priority_icon = "🔴" if rec['priority'] == 'high' else "🟡" if rec['priority'] == 'medium' else "🟢"
                print(f"  {priority_icon} {rec['message']}")
                print(f"     建议行动: {rec['action']}")
        
        print("\n" + "="*80)
