"""
分裂检测分析器
检测同一时间段内多个ID的轨迹分裂事件
"""

from typing import List, Dict, Any
import logging
from datetime import datetime

from .base_analyzer import BaseAnomalyAnalyzer, AnalysisResult

logger = logging.getLogger(__name__)


class SplitDetectionAnalyzer(BaseAnomalyAnalyzer):
    """分裂检测分析器"""
    
    def __init__(self, config: Dict[str, Any], name: str = None):
        super().__init__(config, name or 'SplitDetectionAnalyzer')
        
        # 提取分裂检测相关配置
        self.min_overlap_duration = config.get('min_split_overlap_duration', 0.1)  # 最小重叠时长(秒)
        self.max_split_distance = config.get('max_split_distance', 50.0)  # 最大分裂距离(米)
        self.enable_spatial_validation = config.get('enable_split_spatial_validation', False)
        
        self.logger.info(f"分裂检测分析器初始化: 最小重叠时长={self.min_overlap_duration}s, "
                        f"空间验证={'启用' if self.enable_spatial_validation else '禁用'}")
    
    @property
    def analysis_type(self) -> str:
        return "split_detection"
    
    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        """
        执行分裂检测分析
        
        Args:
            trajectory_chain: 轨迹链数据
            **kwargs: 其他参数
            
        Returns:
            AnalysisResult: 分析结果
        """
        self.log_analysis_start(trajectory_chain, **kwargs)
        
        if not self.validate_input(trajectory_chain, **kwargs):
            return self.create_result(False, "输入数据验证失败")
        
        try:
            result = self.create_result()
            
            # 执行分裂检测
            split_events = self._detect_splits(trajectory_chain)
            
            # 添加事件到结果
            for event in split_events:
                result.add_event(event)
            
            # 添加统计信息
            result.add_statistic('total_splits', len(split_events))
            result.add_statistic('trajectory_segments', len(trajectory_chain))
            
            # 按分裂类型统计
            split_types = {}
            for event in split_events:
                event_type = event.get('type', 'unknown')
                split_types[event_type] = split_types.get(event_type, 0) + 1
            result.add_statistic('split_types', split_types)
            
            # 添加元数据
            result.add_metadata('analysis_method', 'time_overlap')
            result.add_metadata('min_overlap_duration', self.min_overlap_duration)
            result.add_metadata('spatial_validation_enabled', self.enable_spatial_validation)
            
            self.log_analysis_complete(result)
            return result
            
        except Exception as e:
            error_msg = f"分裂检测分析失败: {str(e)}"
            self.logger.error(error_msg)
            return self.create_result(False, error_msg)
    
    def _detect_splits(self, trajectory_chain: List[Any]) -> List[Dict[str, Any]]:
        """
        检测分裂事件
        
        Args:
            trajectory_chain: 轨迹链
            
        Returns:
            List[Dict[str, Any]]: 分裂事件列表
        """
        split_events = []
        
        if len(trajectory_chain) < 2:
            self.logger.info("轨迹段数量少于2个，无需分裂分析")
            return split_events
        
        # 按时间排序
        sorted_chain = sorted(trajectory_chain, key=lambda x: x.start_time)
        
        self.logger.info(f"开始分裂检测，轨迹段数量: {len(sorted_chain)}")
        
        # 遍历所有轨迹段对，寻找时间重叠的不同ID段
        for i in range(len(sorted_chain)):
            for j in range(i + 1, len(sorted_chain)):
                seg1 = sorted_chain[i]
                seg2 = sorted_chain[j]
                
                # 检查是否是不同ID且时间重叠
                if seg1.id != seg2.id and self._segments_time_overlap(seg1, seg2):
                    overlap_info = self._calculate_overlap_info(seg1, seg2)
                    
                    # 检查重叠时长是否满足最小阈值
                    if overlap_info['overlap_duration'] >= self.min_overlap_duration:
                        self.logger.info(f"发现时间重叠段: {seg1.id} 与 {seg2.id}, "
                                       f"重叠时长: {overlap_info['overlap_duration']:.1f}s")
                        
                        # 创建分裂事件
                        split_event = self._create_split_event(seg1, seg2, overlap_info)
                        
                        # 可选的空间验证
                        if self.enable_spatial_validation:
                            spatial_valid = self._validate_split_spatially(seg1, seg2, overlap_info)
                            split_event['spatial_validation'] = spatial_valid
                            if not spatial_valid:
                                split_event['type'] = 'split_spatial_rejected'
                                self.logger.info(f"分裂事件空间验证失败: {seg1.id} vs {seg2.id}")
                        
                        split_events.append(split_event)
        
        self.logger.info(f"分裂检测完成，发现 {len(split_events)} 个分裂事件")
        return split_events
    
    def _segments_time_overlap(self, seg1, seg2) -> bool:
        """检查两个轨迹段是否有时间重叠"""
        return not (seg1.end_time <= seg2.start_time or 
                   seg1.start_time >= seg2.end_time)
    
    def _calculate_overlap_info(self, seg1, seg2) -> Dict[str, Any]:
        """计算重叠信息"""
        overlap_start = max(seg1.start_time, seg2.start_time)
        overlap_end = min(seg1.end_time, seg2.end_time)
        overlap_duration = (overlap_end - overlap_start).total_seconds()
        
        return {
            'overlap_start': overlap_start,
            'overlap_end': overlap_end,
            'overlap_duration': overlap_duration,
            'seg1_duration': seg1.duration,
            'seg2_duration': seg2.duration,
            'overlap_ratio_seg1': overlap_duration / seg1.duration if seg1.duration > 0 else 0,
            'overlap_ratio_seg2': overlap_duration / seg2.duration if seg2.duration > 0 else 0
        }
    
    def _create_split_event(self, seg1, seg2, overlap_info: Dict[str, Any]) -> Dict[str, Any]:
        """创建分裂事件"""
        return {
            'type': 'split_confirmed',
            'timestamp': overlap_info['overlap_start'],
            'ids': [seg1.id, seg2.id],
            'overlap_duration': overlap_info['overlap_duration'],
            'overlap_start': overlap_info['overlap_start'],
            'overlap_end': overlap_info['overlap_end'],
            'overlap_ratio_seg1': overlap_info['overlap_ratio_seg1'],
            'overlap_ratio_seg2': overlap_info['overlap_ratio_seg2'],
            'seg1_info': {
                'id': seg1.id,
                'start_time': seg1.start_time,
                'end_time': seg1.end_time,
                'duration': seg1.duration,
                'point_count': len(seg1.points) if hasattr(seg1, 'points') else 0
            },
            'seg2_info': {
                'id': seg2.id,
                'start_time': seg2.start_time,
                'end_time': seg2.end_time,
                'duration': seg2.duration,
                'point_count': len(seg2.points) if hasattr(seg2, 'points') else 0
            },
            'analysis_method': 'time_overlap',
            'analyzer': self.name
        }
    
    def _validate_split_spatially(self, seg1, seg2, overlap_info: Dict[str, Any]) -> bool:
        """
        空间验证分裂事件
        检查重叠时间段内两个轨迹的空间距离
        
        Args:
            seg1, seg2: 轨迹段
            overlap_info: 重叠信息
            
        Returns:
            bool: 是否通过空间验证
        """
        try:
            # 获取重叠时间段内的点
            overlap_start = overlap_info['overlap_start']
            overlap_end = overlap_info['overlap_end']
            
            seg1_overlap_points = [
                p for p in seg1.points 
                if overlap_start <= p.timestamp <= overlap_end
            ] if hasattr(seg1, 'points') else []
            
            seg2_overlap_points = [
                p for p in seg2.points 
                if overlap_start <= p.timestamp <= overlap_end
            ] if hasattr(seg2, 'points') else []
            
            if not seg1_overlap_points or not seg2_overlap_points:
                return False
            
            # 计算平均距离
            from ..data_utils import GeoUtils
            geo_utils = GeoUtils()
            
            total_distance = 0
            count = 0
            
            for p1 in seg1_overlap_points:
                for p2 in seg2_overlap_points:
                    distance = geo_utils.haversine_distance(
                        p1.lat, p1.lon, p2.lat, p2.lon
                    )
                    total_distance += distance
                    count += 1
            
            if count == 0:
                return False
            
            avg_distance = total_distance / count
            
            # 如果平均距离小于阈值，认为是真实分裂
            return avg_distance <= self.max_split_distance
            
        except Exception as e:
            self.logger.warning(f"空间验证失败: {e}")
            return False
