"""
异常分析基础接口和数据结构
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field
import logging

logger = logging.getLogger(__name__)


@dataclass
class AnalysisResult:
    """分析结果数据结构"""
    analyzer_name: str
    analysis_type: str
    timestamp: datetime = field(default_factory=datetime.now)
    events: List[Dict[str, Any]] = field(default_factory=list)
    statistics: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    success: bool = True
    error_message: Optional[str] = None
    
    def add_event(self, event: Dict[str, Any]):
        """添加事件"""
        self.events.append(event)
    
    def add_statistic(self, key: str, value: Any):
        """添加统计信息"""
        self.statistics[key] = value
    
    def add_metadata(self, key: str, value: Any):
        """添加元数据"""
        self.metadata[key] = value
    
    def get_event_count(self) -> int:
        """获取事件数量"""
        return len(self.events)

    def get_statistic(self, key: str, default=None):
        """获取统计信息"""
        return self.statistics.get(key, default)

    def get_metadata(self, key: str, default=None):
        """获取元数据"""
        return self.metadata.get(key, default)

    def get_events_by_type(self, event_type: str) -> List[Dict[str, Any]]:
        """根据类型获取事件"""
        return [event for event in self.events if event.get('type') == event_type]
    
    def get_events_by_type(self, event_type: str) -> List[Dict[str, Any]]:
        """按类型获取事件"""
        return [event for event in self.events if event.get('type') == event_type]


class BaseAnomalyAnalyzer(ABC):
    """异常分析器基础接口"""
    
    def __init__(self, config: Dict[str, Any], name: str = None):
        """
        初始化分析器
        
        Args:
            config: 配置字典
            name: 分析器名称，如果为None则使用类名
        """
        self.config = config
        self.name = name or self.__class__.__name__
        self.enabled = config.get(f'{self.name.lower()}_enabled', True)
        self.logger = logging.getLogger(f"{__name__}.{self.name}")
        
        # 从配置中提取通用参数
        self._extract_common_config()
        
        self.logger.info(f"初始化异常分析器: {self.name}, 启用状态: {self.enabled}")
    
    def _extract_common_config(self):
        """提取通用配置参数"""
        # 子类可以重写此方法来提取特定配置
        pass
    
    @abstractmethod
    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        """
        执行异常分析
        
        Args:
            trajectory_chain: 轨迹链数据
            **kwargs: 其他分析参数
            
        Returns:
            AnalysisResult: 分析结果
        """
        pass
    
    @property
    @abstractmethod
    def analysis_type(self) -> str:
        """返回分析类型标识"""
        pass
    
    def is_enabled(self) -> bool:
        """检查分析器是否启用"""
        return self.enabled
    
    def set_enabled(self, enabled: bool):
        """设置分析器启用状态"""
        self.enabled = enabled
        self.logger.info(f"分析器 {self.name} 启用状态更新为: {enabled}")
    
    def validate_input(self, trajectory_chain: List[Any], **kwargs) -> bool:
        """
        验证输入数据
        
        Args:
            trajectory_chain: 轨迹链数据
            **kwargs: 其他参数
            
        Returns:
            bool: 验证是否通过
        """
        if not trajectory_chain:
            self.logger.warning("轨迹链为空")
            return False
        return True
    
    def create_result(self, success: bool = True, error_message: str = None) -> AnalysisResult:
        """
        创建分析结果对象
        
        Args:
            success: 是否成功
            error_message: 错误信息
            
        Returns:
            AnalysisResult: 分析结果对象
        """
        return AnalysisResult(
            analyzer_name=self.name,
            analysis_type=self.analysis_type,
            success=success,
            error_message=error_message
        )
    
    def log_analysis_start(self, trajectory_chain: List[Any], **kwargs):
        """记录分析开始"""
        self.logger.info(f"开始 {self.analysis_type} 分析，轨迹段数量: {len(trajectory_chain)}")
    
    def log_analysis_complete(self, result: AnalysisResult):
        """记录分析完成"""
        if result.success:
            self.logger.info(f"{self.analysis_type} 分析完成，发现 {result.get_event_count()} 个事件")
        else:
            self.logger.error(f"{self.analysis_type} 分析失败: {result.error_message}")


class AnalyzerRegistry:
    """分析器注册表"""
    
    def __init__(self):
        self._analyzers = {}
    
    def register(self, analyzer_class: type, name: str = None):
        """注册分析器类"""
        analyzer_name = name or analyzer_class.__name__
        self._analyzers[analyzer_name] = analyzer_class
        logger.info(f"注册分析器: {analyzer_name}")
    
    def get_analyzer_class(self, name: str) -> Optional[type]:
        """获取分析器类"""
        return self._analyzers.get(name)
    
    def list_analyzers(self) -> List[str]:
        """列出所有注册的分析器"""
        return list(self._analyzers.keys())
    
    def create_analyzer(self, name: str, config: Dict[str, Any]) -> Optional[BaseAnomalyAnalyzer]:
        """创建分析器实例"""
        analyzer_class = self.get_analyzer_class(name)
        if analyzer_class:
            return analyzer_class(config)
        return None


# 全局分析器注册表
analyzer_registry = AnalyzerRegistry()
