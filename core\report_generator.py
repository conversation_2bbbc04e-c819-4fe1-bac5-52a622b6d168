"""
精度分析报告生成器
"""

import os
import json
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path


class AccuracyReportGenerator:
    """精度分析报告生成器"""
    
    def __init__(self, template_dir: str = "templates"):
        """
        初始化报告生成器
        
        Args:
            template_dir: 模板文件目录
        """
        self.template_dir = Path(template_dir)
        self.template_path = self.template_dir / "accuracy_report_template.html"
    
    def generate_html_report(self, accuracy_result, output_path: str) -> bool:
        """
        生成HTML格式的精度分析报告
        
        Args:
            accuracy_result: 精度分析结果
            output_path: 输出文件路径
            
        Returns:
            bool: 生成是否成功
        """
        try:
            # 检查模板文件是否存在
            if not self.template_path.exists():
                print(f"警告: 模板文件不存在 {self.template_path}")
                return False
            
            # 读取模板
            with open(self.template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # 准备模板数据
            template_data = self._prepare_template_data(accuracy_result)
            
            # 替换模板变量
            report_content = self._render_template(template_content, template_data)
            
            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 写入报告文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            print(f"✅ 精度分析HTML报告已生成: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 生成HTML报告失败: {e}")
            return False
    
    def _prepare_template_data(self, accuracy_result) -> Dict[str, Any]:
        """准备模板数据"""
        try:
            statistical_summary = accuracy_result.metadata.get('statistical_summary', {})
            samples_count = accuracy_result.metadata.get('samples_count', 0)
            common_time_range = accuracy_result.metadata.get('common_time_range', {})
            
            # 基础信息
            data = {
                'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_samples': samples_count,
                'analysis_duration': common_time_range.get('duration', 0),
                'sampling_rate': round(samples_count / max(common_time_range.get('duration', 1), 1), 1)
            }
            
            # 定位精度数据
            position_accuracy = statistical_summary.get('position_accuracy', {})
            if position_accuracy:
                data.update({
                    'position_mean_error': round(position_accuracy.get('mean', 0), 2),
                    'position_max_error': round(position_accuracy.get('max', 0), 2),
                    'position_p95_error': round(position_accuracy.get('p95', 0), 2),
                    'position_std_error': round(position_accuracy.get('std', 0), 2),
                    'position_quality': self._get_position_quality(position_accuracy.get('mean', 0)),
                    'position_quality_text': self._get_quality_text(self._get_position_quality(position_accuracy.get('mean', 0)))
                })
            
            # 速度精度数据
            speed_accuracy = statistical_summary.get('speed_accuracy', {})
            if speed_accuracy:
                abs_acc = speed_accuracy.get('absolute', {})
                rel_acc = speed_accuracy.get('relative', {})
                data.update({
                    'speed_abs_mean_error': round(abs_acc.get('mean', 0), 2),
                    'speed_rel_mean_error': round(rel_acc.get('mean', 0), 1),
                    'speed_abs_max_error': round(abs_acc.get('max', 0), 2),
                    'speed_abs_p95_error': round(abs_acc.get('p95', 0), 2),
                    'speed_quality': self._get_speed_quality(abs_acc.get('mean', 0)),
                    'speed_quality_text': self._get_quality_text(self._get_speed_quality(abs_acc.get('mean', 0)))
                })
            
            # 航向角精度数据
            heading_accuracy = statistical_summary.get('heading_accuracy', {})
            if heading_accuracy:
                data.update({
                    'heading_mean_error': round(heading_accuracy.get('mean', 0), 1),
                    'heading_max_error': round(heading_accuracy.get('max', 0), 1),
                    'heading_p95_error': round(heading_accuracy.get('p95', 0), 1),
                    'heading_std_error': round(heading_accuracy.get('std', 0), 1),
                    'heading_quality': self._get_heading_quality(heading_accuracy.get('mean', 0)),
                    'heading_quality_text': self._get_quality_text(self._get_heading_quality(heading_accuracy.get('mean', 0)))
                })
            
            # 综合质量评估
            overall_quality = self._calculate_overall_quality(data)
            data.update({
                'overall_quality': overall_quality,
                'overall_quality_text': self._get_quality_text(overall_quality),
                'overall_quality_score': self._calculate_quality_score(overall_quality)
            })
            
            # 异常事件数据
            accuracy_events = accuracy_result.get_events_by_type(['high_position_error', 'high_speed_error', 'high_heading_error'])
            data.update({
                'total_high_error_events': len(accuracy_events),
                'high_position_errors': len(accuracy_result.get_events_by_type('high_position_error')),
                'high_speed_errors': len(accuracy_result.get_events_by_type('high_speed_error')),
                'high_heading_errors': len(accuracy_result.get_events_by_type('high_heading_error')),
                'anomaly_rate': round(len(accuracy_events) / max(samples_count, 1) * 100, 1),
                'accuracy_events': self._format_events_for_template(accuracy_events[:10])  # 只显示前10个
            })
            
            # 精度等级分布
            accuracy_distribution = statistical_summary.get('accuracy_distribution', {})
            if accuracy_distribution:
                data['position_levels'] = self._format_accuracy_levels(
                    accuracy_distribution.get('position', {})
                )
            
            # 改进建议
            data['recommendations'] = self._generate_recommendations_for_template(data)

            # 详细数据表格
            data['detailed_data'] = self._prepare_detailed_data(accuracy_result)

            return data
            
        except Exception as e:
            print(f"准备模板数据失败: {e}")
            return {'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    
    def _render_template(self, template_content: str, data: Dict[str, Any]) -> str:
        """渲染模板"""
        # 简单的模板变量替换
        content = template_content
        
        # 替换单个变量 {{variable}}
        for key, value in data.items():
            if not isinstance(value, (list, dict)):
                content = content.replace(f'{{{{{key}}}}}', str(value))
        
        # 处理列表变量 {{#list}} ... {{/list}}
        content = self._render_list_variables(content, data)
        
        return content
    
    def _render_list_variables(self, content: str, data: Dict[str, Any]) -> str:
        """渲染列表变量"""
        import re
        
        # 处理 {{#list}} ... {{/list}} 模式
        list_pattern = r'\{\{#(\w+)\}\}(.*?)\{\{/\1\}\}'
        
        def replace_list(match):
            list_name = match.group(1)
            list_template = match.group(2)
            
            if list_name in data and isinstance(data[list_name], list):
                result = ""
                for item in data[list_name]:
                    item_content = list_template
                    if isinstance(item, dict):
                        for key, value in item.items():
                            item_content = item_content.replace(f'{{{{{key}}}}}', str(value))
                    result += item_content
                return result
            return ""
        
        return re.sub(list_pattern, replace_list, content, flags=re.DOTALL)
    
    def _get_position_quality(self, mean_error: float) -> str:
        """获取定位质量等级"""
        if mean_error <= 1.0:
            return 'excellent'
        elif mean_error <= 2.0:
            return 'good'
        elif mean_error <= 5.0:
            return 'fair'
        else:
            return 'poor'
    
    def _get_speed_quality(self, mean_error: float) -> str:
        """获取速度质量等级"""
        if mean_error <= 1.0:
            return 'excellent'
        elif mean_error <= 2.0:
            return 'good'
        elif mean_error <= 5.0:
            return 'fair'
        else:
            return 'poor'
    
    def _get_heading_quality(self, mean_error: float) -> str:
        """获取航向角质量等级"""
        if mean_error <= 5.0:
            return 'excellent'
        elif mean_error <= 10.0:
            return 'good'
        elif mean_error <= 30.0:
            return 'fair'
        else:
            return 'poor'
    
    def _get_quality_text(self, quality: str) -> str:
        """获取质量文本描述"""
        quality_texts = {
            'excellent': '优秀',
            'good': '良好',
            'fair': '一般',
            'poor': '较差',
            'unknown': '未知'
        }
        return quality_texts.get(quality, '未知')
    
    def _calculate_overall_quality(self, data: Dict[str, Any]) -> str:
        """计算综合质量等级"""
        try:
            pos_quality = data.get('position_quality', 'poor')
            speed_quality = data.get('speed_quality', 'poor')
            heading_quality = data.get('heading_quality', 'poor')
            
            quality_scores = {'excellent': 4, 'good': 3, 'fair': 2, 'poor': 1}
            avg_score = (quality_scores.get(pos_quality, 1) + 
                        quality_scores.get(speed_quality, 1) + 
                        quality_scores.get(heading_quality, 1)) / 3
            
            if avg_score >= 3.5:
                return 'excellent'
            elif avg_score >= 2.5:
                return 'good'
            elif avg_score >= 1.5:
                return 'fair'
            else:
                return 'poor'
        except Exception:
            return 'unknown'
    
    def _calculate_quality_score(self, quality: str) -> float:
        """计算质量评分"""
        quality_scores = {
            'excellent': 4.0,
            'good': 3.0,
            'fair': 2.0,
            'poor': 1.0,
            'unknown': 0.0
        }
        return quality_scores.get(quality, 0.0)
    
    def _format_events_for_template(self, events: List[Dict]) -> List[Dict]:
        """格式化事件数据用于模板"""
        formatted_events = []
        for event in events:
            event_type = event.get('event_type', '')
            event_type_texts = {
                'high_position_error': '定位精度异常',
                'high_speed_error': '速度精度异常',
                'high_heading_error': '航向角精度异常'
            }
            
            formatted_events.append({
                'event_type_text': event_type_texts.get(event_type, event_type),
                'timestamp': event.get('timestamp', ''),
                'error_value': round(event.get('details', {}).get('error_value', 0), 2),
                'threshold': round(event.get('details', {}).get('threshold', 0), 2)
            })
        
        return formatted_events
    
    def _format_accuracy_levels(self, level_data: Dict[str, Any]) -> List[Dict]:
        """格式化精度等级数据"""
        levels = []
        level_names = {
            'level_1': '高精度',
            'level_2': '中等精度',
            'level_3': '低精度',
            'level_4': '较差精度'
        }
        
        for key, value in level_data.items():
            if 'level_' in key and isinstance(value, dict):
                levels.append({
                    'level_name': level_names.get(key, key),
                    'threshold': value.get('threshold', 0),
                    'count': value.get('count', 0),
                    'percentage': round(value.get('percentage', 0), 1)
                })
        
        return sorted(levels, key=lambda x: x['threshold'])
    
    def _generate_recommendations_for_template(self, data: Dict[str, Any]) -> List[Dict]:
        """生成改进建议用于模板"""
        recommendations = []
        
        # 定位精度建议
        pos_quality = data.get('position_quality', 'poor')
        if pos_quality == 'poor':
            recommendations.append({
                'priority': 'high',
                'message': f"定位精度较差，平均误差 {data.get('position_mean_error', 0)}m",
                'action': '检查RTK基站信号质量和感知系统标定'
            })
        elif pos_quality == 'fair':
            recommendations.append({
                'priority': 'medium',
                'message': f"定位精度一般，平均误差 {data.get('position_mean_error', 0)}m",
                'action': '优化感知算法或检查传感器标定'
            })
        
        # 速度精度建议
        speed_quality = data.get('speed_quality', 'poor')
        if speed_quality == 'poor':
            recommendations.append({
                'priority': 'high',
                'message': f"速度精度较差，平均误差 {data.get('speed_abs_mean_error', 0)}m/s",
                'action': '检查速度估算算法和传感器融合策略'
            })
        
        # 航向角精度建议
        heading_quality = data.get('heading_quality', 'poor')
        if heading_quality == 'poor':
            recommendations.append({
                'priority': 'high',
                'message': f"航向角精度较差，平均误差 {data.get('heading_mean_error', 0)}°",
                'action': '检查IMU标定和航向角估算算法'
            })
        
        # 异常率建议
        anomaly_rate = data.get('anomaly_rate', 0)
        if anomaly_rate > 20:
            recommendations.append({
                'priority': 'high',
                'message': f"精度异常率过高 ({anomaly_rate}%)",
                'action': '全面检查感知系统和数据质量'
            })
        elif anomaly_rate > 10:
            recommendations.append({
                'priority': 'medium',
                'message': f"精度异常率较高 ({anomaly_rate}%)",
                'action': '重点检查异常时段的数据质量'
            })
        
        # 如果没有问题，给出优化建议
        if not recommendations:
            recommendations.append({
                'priority': 'low',
                'message': '系统精度表现良好',
                'action': '可考虑进一步优化算法参数以提升性能'
            })
        
        return recommendations

    def _prepare_detailed_data(self, accuracy_result):
        """准备详细数据表格"""
        try:
            detailed_data = []

            # 从accuracy_result的metadata中获取详细数据
            accuracy_data = accuracy_result.metadata.get('detailed_accuracy_data', [])

            if not accuracy_data:
                print("⚠️ 未找到详细精度数据")
                return []

            for item in accuracy_data:
                # 获取误差值
                position_error = item.get('position_error', 0)
                speed_abs_error = item.get('speed_abs_error', 0)
                heading_error = item.get('heading_error', 0)

                # 确定质量等级
                quality_level = self._determine_data_quality(position_error, speed_abs_error, heading_error)

                # 格式化数据
                detailed_item = {
                    'timestamp': item.get('timestamp', ''),
                    'perception_id': item.get('perception_id', ''),
                    'position_error': f"{position_error:.3f}",
                    'speed_abs_error': f"{speed_abs_error:.2f}",
                    'heading_error': f"{heading_error:.1f}",
                    'quality_level': quality_level,
                    'quality_text': self._get_quality_text(quality_level),
                    'position_error_color': self._get_error_color(position_error, [1.0, 2.0, 5.0]),
                    'speed_error_color': self._get_error_color(speed_abs_error, [1.0, 3.0, 5.0]),
                    'heading_error_color': self._get_error_color(heading_error, [5.0, 10.0, 30.0])
                }

                detailed_data.append(detailed_item)

            # 按时间戳排序
            detailed_data.sort(key=lambda x: x['timestamp'])

            print(f"📊 准备详细数据完成，共 {len(detailed_data)} 条记录")
            return detailed_data

        except Exception as e:
            print(f"❌ 准备详细数据失败: {e}")
            return []

    def _determine_data_quality(self, position_error, speed_error, heading_error):
        """根据误差值确定数据质量等级"""
        # 定义阈值
        pos_thresholds = [1.0, 2.0, 5.0]  # 优秀, 良好, 一般
        speed_thresholds = [1.0, 3.0, 5.0]
        heading_thresholds = [5.0, 10.0, 30.0]

        # 计算各项质量等级
        pos_level = 0 if position_error <= pos_thresholds[0] else \
                   1 if position_error <= pos_thresholds[1] else \
                   2 if position_error <= pos_thresholds[2] else 3

        speed_level = 0 if speed_error <= speed_thresholds[0] else \
                     1 if speed_error <= speed_thresholds[1] else \
                     2 if speed_error <= speed_thresholds[2] else 3

        heading_level = 0 if heading_error <= heading_thresholds[0] else \
                       1 if heading_error <= heading_thresholds[1] else \
                       2 if heading_error <= heading_thresholds[2] else 3

        # 取最差的等级作为综合等级
        worst_level = max(pos_level, speed_level, heading_level)

        quality_map = {0: 'excellent', 1: 'good', 2: 'fair', 3: 'poor'}
        return quality_map.get(worst_level, 'poor')

    def _get_error_color(self, error_value, thresholds):
        """根据误差值获取颜色"""
        if error_value <= thresholds[0]:
            return '#28a745'  # 绿色 - 优秀
        elif error_value <= thresholds[1]:
            return '#ffc107'  # 黄色 - 良好
        elif error_value <= thresholds[2]:
            return '#fd7e14'  # 橙色 - 一般
        else:
            return '#dc3545'  # 红色 - 较差
