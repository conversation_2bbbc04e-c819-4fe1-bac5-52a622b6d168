# 统一评分系统快速上手指南

## 🚀 5分钟快速开始

### 1. 立即体验统一评分
```bash
# 使用真实测试数据，一键体验统一评分效果
python main.py --rtk ./data/rtk_part005.txt --perception ./data/AJ06993PAJ00115B1.txt --config config/unified_config.json --verbose
```

**预期结果**:
- 匹配轨迹时长: 20.2秒
- 平均匹配分数: 0.897 ✅
- 成功匹配2个高质量轨迹段

### 2. 对比评分效果
```bash
# 运行评分对比测试
python test_realistic_unified_scoring.py
```

**预期输出**:
```
=== 完美短轨迹 ===
传统评分: 0.650 ❌  
统一评分: 0.867 ✅  (short_trajectory策略)

=== 中间优秀长轨迹 ===  
传统评分: 0.650 ❌
统一评分: 0.761 ✅  (traditional策略)
```

## ⚙️ 配置选择指南

### 场景1: 高精度匹配（推荐）
```json
{
  "scoring": {
    "method": "unified",
    "local_match_thr": 0.7,
    "quality_threshold_high": 0.8,
    "short_trajectory_threshold": 10.0
  }
}
```
**适用**: 大多数生产环境，平衡精度和召回率

### 场景2: 高召回率匹配
```json
{
  "scoring": {
    "method": "unified", 
    "local_match_thr": 0.6,
    "quality_threshold_high": 0.7,
    "short_trajectory_threshold": 12.0
  }
}
```
**适用**: 希望匹配更多轨迹，容忍一定质量下降

### 场景3: 高精度匹配
```json
{
  "scoring": {
    "method": "unified",
    "local_match_thr": 0.8, 
    "quality_threshold_high": 0.85,
    "short_trajectory_threshold": 8.0
  }
}
```
**适用**: 对质量要求极高，宁缺毋滥

### 场景4: 兼容传统系统
```json
{
  "scoring": {
    "method": "legacy",
    "local_match_thr": 0.8,
    "peak_weight": 0.6,
    "duration_weight": 0.3,
    "stability_weight": 0.1
  }
}
```
**适用**: 需要与历史结果保持一致

## 🎯 常用命令速查

### 基本使用
```bash
# 统一评分（推荐）
python main.py --rtk data/rtk.csv --perception data/perception.csv --config config/unified_config.json

# 传统评分
python main.py --rtk data/rtk.csv --perception data/perception.csv --config config/default.json

# 详细输出
python main.py --rtk data/rtk.csv --perception data/perception.csv --config config/unified_config.json --verbose
```

### 测试验证
```bash
# 统一评分实现测试
python test_unified_scoring_implementation.py

# 真实场景评分测试  
python test_realistic_unified_scoring.py

# 完整功能测试
python tests/test_complete.py
```

### 结果分析
```bash
# 查看匹配结果
ls output/results/

# 分析诊断信息
cat output/results/*_diagnostic.json | jq '.statistics'

# 查看评分详情
grep "策略=" output.log
```

## 📊 评分策略速查表

| 轨迹特征 | 自动选择策略 | 评分重点 | 典型分数范围 |
|---------|-------------|---------|-------------|
| ≤10秒 高质量 | short_trajectory | 空间质量 | 0.8-0.95 |
| >10秒 峰值≥0.8 | peak_quality_priority | 最佳5秒窗口 | 0.75-0.95 |
| >10秒 多高质量段 | segment_quality_priority | 高质量段占比 | 0.7-0.9 |
| >10秒 一般质量 | traditional_long_trajectory | 综合评估 | 0.3-0.7 |

## 🔧 快速故障排除

### 问题1: 分数都是0.000
```bash
# 检查错误日志
python main.py --config config/unified_config.json --verbose 2>&1 | grep -i error

# 常见原因: GeoUtils方法调用错误
# 解决: 确保代码中使用 geo_utils.haversine_distance()
```

### 问题2: 所有轨迹都被拒绝
```bash
# 降低匹配阈值
# 修改config/unified_config.json:
"local_match_thr": 0.6  # 从0.7降到0.6
```

### 问题3: 短轨迹仍被拒绝
```bash
# 调整短轨迹参数
# 修改config/unified_config.json:
"short_trajectory_threshold": 12.0,  # 扩大短轨迹范围
"spatial_decay_distance": 8.0        # 增加空间容忍度
```

### 问题4: 长轨迹无法识别优势
```bash
# 调整质量阈值
# 修改config/unified_config.json:
"quality_threshold_high": 0.7,       # 降低高质量阈值
"peak_window_duration": 3.0          # 减小峰值窗口
```

## 📈 效果验证清单

### ✅ 基本功能验证
- [ ] 统一评分系统正常启动
- [ ] 评分策略自动选择工作正常
- [ ] 输出文件包含final_score列
- [ ] 日志显示策略选择信息

### ✅ 短轨迹改进验证
- [ ] 高质量短轨迹(≤10秒)分数≥0.7
- [ ] 使用short_trajectory策略
- [ ] 相比传统方法分数提升明显

### ✅ 长轨迹改进验证  
- [ ] 局部优秀长轨迹分数提升
- [ ] 使用peak_quality_priority或segment_quality_priority策略
- [ ] 不被低质量段拖累

### ✅ 系统稳定性验证
- [ ] 处理大规模数据无异常
- [ ] 内存使用正常
- [ ] 处理时间可接受
- [ ] 低质量轨迹仍被正确拒绝

## 🎯 下一步行动

### 1. 生产部署准备
```bash
# 1. 备份现有配置
cp config/default.json config/default_backup.json

# 2. 部署统一评分配置
cp config/unified_config.json config/production.json

# 3. 小规模测试
python main.py --config config/production.json --rtk test_data/rtk.csv --perception test_data/perception.csv
```

### 2. 参数调优
```bash
# 1. 收集一周的评分数据
# 2. 分析分数分布和策略使用情况
# 3. 根据实际效果调整参数
# 4. A/B测试验证改进效果
```

### 3. 监控设置
```bash
# 1. 设置评分分布监控
# 2. 监控各策略使用频率
# 3. 设置异常分数告警
# 4. 定期评估匹配质量
```

## 📞 技术支持

### 常用资源
- **详细文档**: `docs/scoring_system_guide.md`
- **配置参考**: `config/unified_config.json`
- **测试用例**: `test_realistic_unified_scoring.py`
- **故障排除**: 查看日志中的ERROR和WARNING信息

### 联系方式
- 遇到问题请查看详细日志输出
- 参考配置文件中的注释说明
- 运行测试用例验证功能正常

**开始使用统一评分系统，享受更智能、更公平的轨迹匹配体验！** 🚀
