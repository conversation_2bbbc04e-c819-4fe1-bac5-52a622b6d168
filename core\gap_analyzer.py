#!/usr/bin/env python3
"""
增强的间隙分析器
替代原来分析工具中的漏检逻辑，使用gap_analysis_tool.py的功能
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional
import math

class EnhancedGapAnalyzer:
    """增强的间隙分析器，集成了gap_analysis_tool.py的功能"""
    
    def __init__(self, config):
        """初始化分析器"""
        # 如果传入的是字符串，则作为配置文件路径读取
        if isinstance(config, str):
            import json
            with open(config, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        else:
            self.config = config
        
        self.min_missing_gap = self.config.get('min_missing_gap', 0.5)
        self.max_missing_gap = self.config.get('max_missing_gap', 5.0)
        self.switch_dt = self.config.get('switch_dt', 2.0)
        self.switch_dist = self.config.get('switch_dist', 10.0)
        self.switch_speed = self.config.get('switch_speed', 5.0)
        self.switch_heading = self.config.get('switch_heading', 30.0)
        self.normal_detection_interval = self.config.get('normal_detection_interval', 0.1)
        self.tolerance_multiplier = self.config.get('tolerance_multiplier', 2.0)  # 容差倍数，默认2倍
        
        # 分析结果存储
        self.analysis_results = {
            'missing_gaps': [],
            'id_connection_gaps': [],
            'internal_time_jumps': [],
            'trajectory_continuity': {},
            'gap_statistics': {}
        }
    
    def identify_missing_gaps(self, final_chain: List, rtk_start_time: datetime, 
                            rtk_end_time: datetime) -> List[Dict]:
        """
        增强的漏检识别方法，替代原来的identify_missing_gaps
        重点关注ID轨迹连接处的漏检，min_missing_gap作为判断漏检的最小阈值
        """
        missing_gaps = []
        
        if not final_chain:
            # 如果没有任何匹配段，整个RTK轨迹都是漏检
            gap_duration = (rtk_end_time - rtk_start_time).total_seconds()
            if gap_duration > self.min_missing_gap:
                missing_gaps.append({
                    'start_time': rtk_start_time,
                    'end_time': rtk_end_time,
                    'duration': gap_duration,
                    'type': 'complete_missing',
                    'severity': 'critical',
                    'analysis_type': 'enhanced'
                })
            return missing_gaps
        
        # 按时间排序
        sorted_chain = sorted(final_chain, key=lambda x: x.start_time)
        
        # 1. RTK开始到第一段的gap（头部漏检）
        first_seg = sorted_chain[0]
        if first_seg.start_time > rtk_start_time:
            gap_duration = (first_seg.start_time - rtk_start_time).total_seconds()
            if gap_duration > self.min_missing_gap:
                gap_info = {
                    'start_time': rtk_start_time,
                    'end_time': first_seg.start_time,
                    'duration': gap_duration,
                    'type': 'head_missing',
                    'severity': self._classify_gap_severity(gap_duration),
                    'analysis_type': 'enhanced',
                    'next_id': first_seg.id if hasattr(first_seg, 'id') else None
                }
                missing_gaps.append(gap_info)
        
        # 2. 段与段之间的gap（重点关注的ID连接处漏检）
        for i in range(len(sorted_chain) - 1):
            current_seg = sorted_chain[i]
            next_seg = sorted_chain[i + 1]
            
            if current_seg.end_time < next_seg.start_time:
                gap_duration = (next_seg.start_time - current_seg.end_time).total_seconds()
                
                # 使用min_missing_gap作为判断阈值
                if gap_duration > self.min_missing_gap:
                    # 分析ID连接处的运动连续性
                    motion_analysis = self._analyze_id_connection_motion(current_seg, next_seg)
                    
                    gap_info = {
                        'start_time': current_seg.end_time,
                        'end_time': next_seg.start_time,
                        'duration': gap_duration,
                        'type': 'middle_missing',
                        'severity': self._classify_gap_severity(gap_duration),
                        'analysis_type': 'enhanced',
                        'prev_id': current_seg.id if hasattr(current_seg, 'id') else None,
                        'next_id': next_seg.id if hasattr(next_seg, 'id') else None,
                        'motion_continuity': motion_analysis['is_continuous'],
                        'motion_analysis': motion_analysis
                    }
                    missing_gaps.append(gap_info)
                    
                    # 记录ID连接处的间隙分析
                    self.analysis_results['id_connection_gaps'].append({
                        'gap_duration': gap_duration,
                        'prev_id': current_seg.id if hasattr(current_seg, 'id') else None,
                        'next_id': next_seg.id if hasattr(next_seg, 'id') else None,
                        'motion_analysis': motion_analysis,
                        'is_problematic': not motion_analysis['is_continuous']
                    })
        
        # 3. 最后一段到RTK结束的gap（尾部漏检）
        last_seg = sorted_chain[-1]
        if last_seg.end_time < rtk_end_time:
            gap_duration = (rtk_end_time - last_seg.end_time).total_seconds()
            if gap_duration > self.min_missing_gap:
                gap_info = {
                    'start_time': last_seg.end_time,
                    'end_time': rtk_end_time,
                    'duration': gap_duration,
                    'type': 'tail_missing',
                    'severity': self._classify_gap_severity(gap_duration),
                    'analysis_type': 'enhanced',
                    'prev_id': last_seg.id if hasattr(last_seg, 'id') else None
                }
                missing_gaps.append(gap_info)
        
        # 存储结果
        self.analysis_results['missing_gaps'] = missing_gaps
        
        return missing_gaps
    
    def _analyze_id_connection_motion(self, prev_seg, next_seg) -> Dict:
        """分析ID连接处的运动连续性"""
        try:
            # 获取连接点
            prev_point = prev_seg.points[-1] if hasattr(prev_seg, 'points') and prev_seg.points else None
            next_point = next_seg.points[0] if hasattr(next_seg, 'points') and next_seg.points else None
            
            if not prev_point or not next_point:
                return {
                    'is_continuous': False,
                    'reason': 'missing_connection_points',
                    'distance': None,
                    'speed_diff': None,
                    'heading_diff': None,
                    'time_diff': None
                }
            
            # 计算各项指标
            distance = self._haversine_distance(
                prev_point.lat, prev_point.lon,
                next_point.lat, next_point.lon
            )
            
            speed_diff = abs(getattr(prev_point, 'speed', 0) - getattr(next_point, 'speed', 0))
            
            prev_heading = getattr(prev_point, 'heading', 0)
            next_heading = getattr(next_point, 'heading', 0)
            heading_diff = abs(prev_heading - next_heading)
            if heading_diff > 180:
                heading_diff = 360 - heading_diff
            
            time_diff = (next_seg.start_time - prev_seg.end_time).total_seconds()
            
            # 四阈值检查
            conditions = {
                'distance_ok': distance <= self.switch_dist,
                'speed_ok': speed_diff <= self.switch_speed,
                'heading_ok': heading_diff <= self.switch_heading,
                'time_ok': time_diff <= self.switch_dt
            }
            
            is_continuous = all(conditions.values())
            
            return {
                'is_continuous': is_continuous,
                'distance': round(distance, 2),
                'speed_diff': round(speed_diff, 2),
                'heading_diff': round(heading_diff, 1),
                'time_diff': round(time_diff, 2),
                'conditions': conditions,
                'thresholds': {
                    'distance_threshold': self.switch_dist,
                    'speed_threshold': self.switch_speed,
                    'heading_threshold': self.switch_heading,
                    'time_threshold': self.switch_dt
                }
            }
            
        except Exception as e:
            return {
                'is_continuous': False,
                'reason': f'analysis_error: {str(e)}',
                'distance': None,
                'speed_diff': None,
                'heading_diff': None,
                'time_diff': None
            }
    
    def _classify_gap_severity(self, gap_duration: float) -> str:
        """根据间隙时长分类严重程度"""
        if gap_duration <= self.min_missing_gap:
            return 'normal'
        elif gap_duration <= self.min_missing_gap * 2:
            return 'minor'
        elif gap_duration <= self.max_missing_gap:
            return 'moderate'
        else:
            return 'severe'
    
    def _haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两点间的距离（米）"""
        R = 6371000  # 地球半径（米）
        
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        return R * c
    
    def analyze_trajectory_internal_jumps(self, matched_data: pd.DataFrame) -> List[Dict]:
        """
        分析轨迹内部时间跳跃
        这是新增的功能，专门检测同一ID轨迹内部的时间跳跃
        """
        internal_jumps = []
        
        if 'perception_id' not in matched_data.columns:
            return internal_jumps
        
        for id_val in matched_data['perception_id'].unique():
            if pd.isna(id_val):
                continue
            
            id_data = matched_data[matched_data['perception_id'] == id_val].sort_values('per_timestamp')
            if len(id_data) < 2:
                continue
            
            # 计算时间间隔
            id_data['per_timestamp'] = pd.to_datetime(id_data['per_timestamp'])
            time_diffs = id_data['per_timestamp'].diff().dt.total_seconds()
            
            # 检测异常间隔
            tolerance = self.normal_detection_interval * self.tolerance_multiplier  # 使用配置的容差倍数
            
            for i, time_diff in enumerate(time_diffs):
                if pd.isna(time_diff):
                    continue
                
                if time_diff > tolerance:
                    jump_info = {
                        'type': 'internal_time_jump',
                        'id': id_val,
                        'jump_duration': time_diff,
                                            'jump_start_time': id_data.iloc[i-1]['per_timestamp'],
                    'jump_end_time': id_data.iloc[i]['per_timestamp'],
                        'expected_interval': self.normal_detection_interval,
                        'actual_interval': time_diff,
                        'severity': 'high' if time_diff > self.min_missing_gap else 'medium',
                        'before_point': self._extract_point_info(id_data.iloc[i-1]),
                        'after_point': self._extract_point_info(id_data.iloc[i])
                    }
                    
                    internal_jumps.append(jump_info)
        
        self.analysis_results['internal_time_jumps'] = internal_jumps
        return internal_jumps
    
    def _extract_point_info(self, row: pd.Series) -> Dict:
        """提取点信息"""
        return {
            'timestamp': row['per_timestamp'] if pd.notna(row.get('per_timestamp')) else None,
            'lat': row.get('perception_lat', row.get('lat')),
            'lon': row.get('perception_lon', row.get('lon')),
            'speed': row.get('perception_speed', row.get('speed')),
            'heading': row.get('perception_heading', row.get('heading'))
        }
    
    def generate_enhanced_gap_report(self) -> Dict:
        """生成增强的间隙分析报告"""
        report = {
            'analysis_type': 'enhanced_gap_analysis',
            'timestamp': datetime.now().isoformat(),
            'configuration': {
                'min_missing_gap': self.min_missing_gap,
                'max_missing_gap': self.max_missing_gap,
                'switch_thresholds': {
                    'time': self.switch_dt,
                    'distance': self.switch_dist,
                    'speed': self.switch_speed,
                    'heading': self.switch_heading
                },
                'tolerance_multiplier': self.tolerance_multiplier
            },
            'results': self.analysis_results,
            'statistics': self._calculate_gap_statistics()
        }
        
        return report
    
    def _calculate_gap_statistics(self) -> Dict:
        """计算间隙统计信息"""
        missing_gaps = self.analysis_results.get('missing_gaps', [])
        id_connection_gaps = self.analysis_results.get('id_connection_gaps', [])
        internal_jumps = self.analysis_results.get('internal_time_jumps', [])
        
        stats = {
            'total_missing_gaps': len(missing_gaps),
            'missing_gap_types': {},
            'total_missing_duration': 0,
            'id_connection_analysis': {
                'total_connections': len(id_connection_gaps),
                'problematic_connections': len([g for g in id_connection_gaps if g.get('is_problematic', False)]),
                'continuous_connections': len([g for g in id_connection_gaps if not g.get('is_problematic', True)])
            },
            'internal_jumps': {
                'total_jumps': len(internal_jumps),
                'high_severity_jumps': len([j for j in internal_jumps if j.get('severity') == 'high']),
                'medium_severity_jumps': len([j for j in internal_jumps if j.get('severity') == 'medium'])
            }
        }
        
        # 统计漏检类型
        for gap in missing_gaps:
            gap_type = gap.get('type', 'unknown')
            if gap_type not in stats['missing_gap_types']:
                stats['missing_gap_types'][gap_type] = 0
            stats['missing_gap_types'][gap_type] += 1
            stats['total_missing_duration'] += gap.get('duration', 0)
        
        return stats
    
    def print_enhanced_analysis_summary(self):
        """打印增强分析摘要"""
        stats = self._calculate_gap_statistics()
        
        print("\n" + "="*60)
        print("📊 增强间隙分析摘要")
        print("="*60)
        
        print(f"\n🔍 漏检分析 (min_missing_gap = {self.min_missing_gap}s):")
        print(f"  • 总漏检间隙数: {stats['total_missing_gaps']}")
        print(f"  • 总漏检时长: {stats['total_missing_duration']:.1f}秒")
        
        if stats['missing_gap_types']:
            print("  • 漏检类型分布:")
            for gap_type, count in stats['missing_gap_types'].items():
                print(f"    - {gap_type}: {count}个")
        
        print(f"\n🔗 ID连接处分析:")
        id_stats = stats['id_connection_analysis']
        print(f"  • 总连接数: {id_stats['total_connections']}")
        print(f"  • 运动连续连接: {id_stats['continuous_connections']}")
        print(f"  • 运动不连续连接: {id_stats['problematic_connections']}")
        
        if id_stats['problematic_connections'] > 0:
            print("  ⚠️  存在运动不连续的ID连接，可能表明:")
            print("     - ID切换时的轨迹跳跃")
            print("     - 感知系统的目标跟踪不稳定")
            print("     - 需要调整切换阈值参数")
        
        print(f"\n⏰ 轨迹内部时间跳跃:")
        jump_stats = stats['internal_jumps']
        print(f"  • 总跳跃数: {jump_stats['total_jumps']}")
        print(f"  • 高严重度跳跃: {jump_stats['high_severity_jumps']}")
        print(f"  • 中等严重度跳跃: {jump_stats['medium_severity_jumps']}")
        
        if jump_stats['total_jumps'] == 0:
            print("  ✅ 未检测到轨迹内部时间跳跃")
        else:
            print("  ⚠️  检测到轨迹内部时间跳跃，可能表明:")
            print("     - 感知系统检测不连续")
            print("     - 数据传输或处理延迟")
            print("     - 需要检查感知系统稳定性")
        
        print("\n" + "="*60) 