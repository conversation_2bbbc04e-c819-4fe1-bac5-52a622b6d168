"""
ID切换分析器
检测轨迹ID的切换事件和运动连续性
"""

from typing import List, Dict, Any
import logging
from datetime import datetime

from .base_analyzer import BaseAnomalyAnalyzer, AnalysisResult

logger = logging.getLogger(__name__)


class IDSwitchAnalyzer(BaseAnomalyAnalyzer):
    """ID切换分析器"""
    
    def __init__(self, config: Dict[str, Any], name: str = None):
        super().__init__(config, name or 'IDSwitchAnalyzer')
        
        # 提取ID切换分析相关配置
        self.max_switch_gap = config.get('max_switch_gap', 5.0)  # 最大切换间隔(秒)
        self.switch_distance_threshold = config.get('switch_dist', 10.0)  # 距离阈值(米)
        self.switch_speed_threshold = config.get('switch_speed', 5.0)  # 速度差阈值(km/h)
        self.switch_heading_threshold = config.get('switch_heading', 30.0)  # 航向差阈值(度)
        self.enable_continuity_check = config.get('enable_switch_continuity_check', True)
        
        self.logger.info(f"ID切换分析器初始化: 最大间隔={self.max_switch_gap}s, "
                        f"距离阈值={self.switch_distance_threshold}m, "
                        f"连续性检查={'启用' if self.enable_continuity_check else '禁用'}")
    
    @property
    def analysis_type(self) -> str:
        return "id_switch"
    
    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        """
        执行ID切换分析
        
        Args:
            trajectory_chain: 轨迹链数据
            **kwargs: 其他参数
            
        Returns:
            AnalysisResult: 分析结果
        """
        self.log_analysis_start(trajectory_chain, **kwargs)
        
        if not self.validate_input(trajectory_chain, **kwargs):
            return self.create_result(False, "输入数据验证失败")
        
        try:
            result = self.create_result()
            
            # 执行ID切换检测
            switch_events = self._detect_id_switches(trajectory_chain)
            
            # 添加事件到结果
            for event in switch_events:
                result.add_event(event)
            
            # 添加统计信息
            result.add_statistic('total_switches', len(switch_events))
            result.add_statistic('trajectory_segments', len(trajectory_chain))
            
            # 按切换类型统计
            switch_types = {}
            continuity_stats = {'continuous': 0, 'discontinuous': 0, 'unknown': 0}
            
            for event in switch_events:
                event_type = event.get('type', 'unknown')
                switch_types[event_type] = switch_types.get(event_type, 0) + 1
                
                # 统计连续性
                if 'continuity_analysis' in event:
                    if event['continuity_analysis'].get('is_continuous', False):
                        continuity_stats['continuous'] += 1
                    else:
                        continuity_stats['discontinuous'] += 1
                else:
                    continuity_stats['unknown'] += 1
            
            result.add_statistic('switch_types', switch_types)
            result.add_statistic('continuity_stats', continuity_stats)
            
            # 添加元数据
            result.add_metadata('analysis_method', 'time_sequence')
            result.add_metadata('max_switch_gap', self.max_switch_gap)
            result.add_metadata('continuity_check_enabled', self.enable_continuity_check)
            result.add_metadata('thresholds', {
                'distance': self.switch_distance_threshold,
                'speed': self.switch_speed_threshold,
                'heading': self.switch_heading_threshold
            })
            
            self.log_analysis_complete(result)
            return result
            
        except Exception as e:
            error_msg = f"ID切换分析失败: {str(e)}"
            self.logger.error(error_msg)
            return self.create_result(False, error_msg)
    
    def _detect_id_switches(self, trajectory_chain: List[Any]) -> List[Dict[str, Any]]:
        """
        检测ID切换事件
        
        Args:
            trajectory_chain: 轨迹链
            
        Returns:
            List[Dict[str, Any]]: ID切换事件列表
        """
        switch_events = []
        
        if len(trajectory_chain) < 2:
            self.logger.info("轨迹段数量少于2个，无需切换分析")
            return switch_events
        
        # 按时间排序
        sorted_chain = sorted(trajectory_chain, key=lambda x: x.start_time)
        
        self.logger.info(f"开始ID切换分析，轨迹段数量: {len(sorted_chain)}")
        
        # 遍历相邻段，检查ID变化
        for i in range(len(sorted_chain) - 1):
            prev_seg = sorted_chain[i]
            next_seg = sorted_chain[i + 1]
            
            # 跳过同ID段
            if prev_seg.id == next_seg.id:
                continue
            
            # 跳过时间重叠段（已在分裂分析中处理）
            if self._segments_time_overlap(prev_seg, next_seg):
                continue
            
            # 计算时间间隔
            gap_duration = (next_seg.start_time - prev_seg.end_time).total_seconds()
            
            # 检查间隔是否在合理范围内
            if gap_duration > self.max_switch_gap:
                self.logger.debug(f"跳过间隔过大的切换: {prev_seg.id} → {next_seg.id}, "
                                f"间隔={gap_duration:.1f}s")
                continue
            
            # 创建切换事件
            switch_event = self._create_switch_event(prev_seg, next_seg, gap_duration)
            
            # 可选的连续性检查
            if self.enable_continuity_check:
                continuity_analysis = self._analyze_continuity(prev_seg, next_seg)
                switch_event['continuity_analysis'] = continuity_analysis
                
                # 根据连续性调整事件类型
                if continuity_analysis.get('is_continuous', False):
                    switch_event['type'] = 'switch_continuous'
                else:
                    switch_event['type'] = 'switch_discontinuous'
            
            switch_events.append(switch_event)
            
            self.logger.info(f"ID切换: {prev_seg.id} → {next_seg.id}, 间隔={gap_duration:.1f}s")
        
        self.logger.info(f"ID切换分析完成，发现 {len(switch_events)} 个切换事件")
        return switch_events
    
    def _segments_time_overlap(self, seg1, seg2) -> bool:
        """检查两个轨迹段是否有时间重叠"""
        return not (seg1.end_time <= seg2.start_time or 
                   seg1.start_time >= seg2.end_time)
    
    def _create_switch_event(self, prev_seg, next_seg, gap_duration: float) -> Dict[str, Any]:
        """创建切换事件"""
        return {
            'type': 'switch',
            'timestamp': next_seg.start_time,
            'from_id': prev_seg.id,
            'to_id': next_seg.id,
            'gap_duration': gap_duration,
            'prev_seg_info': {
                'id': prev_seg.id,
                'start_time': prev_seg.start_time,
                'end_time': prev_seg.end_time,
                'duration': prev_seg.duration,
                'point_count': len(prev_seg.points) if hasattr(prev_seg, 'points') else 0
            },
            'next_seg_info': {
                'id': next_seg.id,
                'start_time': next_seg.start_time,
                'end_time': next_seg.end_time,
                'duration': next_seg.duration,
                'point_count': len(next_seg.points) if hasattr(next_seg, 'points') else 0
            },
            'analysis_method': 'time_sequence',
            'analyzer': self.name
        }
    
    def _analyze_continuity(self, prev_seg, next_seg) -> Dict[str, Any]:
        """
        分析运动连续性
        
        Args:
            prev_seg: 前一个轨迹段
            next_seg: 后一个轨迹段
            
        Returns:
            Dict[str, Any]: 连续性分析结果
        """
        try:
            # 初始化默认值
            distance = 0.0
            speed_diff = 0.0
            heading_diff = 0.0
            
            if (hasattr(prev_seg, 'points') and prev_seg.points and 
                hasattr(next_seg, 'points') and next_seg.points):
                
                # 获取前一段最后一点和后一段第一点
                prev_point = prev_seg.points[-1]
                next_point = next_seg.points[0]
                
                # 计算位置距离
                distance = self._calculate_distance(prev_point, next_point)
                
                # 计算速度差异
                prev_speed = self._get_point_speed(prev_point)
                next_speed = self._get_point_speed(next_point)
                speed_diff = abs(next_speed - prev_speed)
                
                # 计算航向差异
                prev_heading = self._get_point_heading(prev_point)
                next_heading = self._get_point_heading(next_point)
                heading_diff = self._calculate_heading_diff(prev_heading, next_heading)
            
            # 连续性判断条件
            conditions = {
                'distance_ok': distance <= self.switch_distance_threshold,
                'speed_ok': speed_diff <= self.switch_speed_threshold,
                'heading_ok': heading_diff <= self.switch_heading_threshold
            }
            
            is_continuous = all(conditions.values())
            
            return {
                'is_continuous': is_continuous,
                'distance': round(distance, 2),
                'speed_diff': round(speed_diff, 2),
                'heading_diff': round(heading_diff, 1),
                'conditions': conditions,
                'thresholds': {
                    'distance_threshold': self.switch_distance_threshold,
                    'speed_threshold': self.switch_speed_threshold,
                    'heading_threshold': self.switch_heading_threshold
                }
            }
            
        except Exception as e:
            self.logger.warning(f"连续性分析失败: {e}")
            return {
                'is_continuous': False,
                'reason': f'analysis_error: {str(e)}',
                'distance': None,
                'speed_diff': None,
                'heading_diff': None
            }
    
    def _calculate_distance(self, point1, point2) -> float:
        """计算两点间距离"""
        try:
            from ..data_utils import GeoUtils
            geo_utils = GeoUtils()
            
            # 处理不同的点数据格式
            lat1 = point1.lat if hasattr(point1, 'lat') else point1.get('lat', 0)
            lon1 = point1.lon if hasattr(point1, 'lon') else point1.get('lon', 0)
            lat2 = point2.lat if hasattr(point2, 'lat') else point2.get('lat', 0)
            lon2 = point2.lon if hasattr(point2, 'lon') else point2.get('lon', 0)
            
            return geo_utils.haversine_distance(lat1, lon1, lat2, lon2)
        except Exception:
            return 0.0
    
    def _get_point_speed(self, point) -> float:
        """获取点的速度"""
        if hasattr(point, 'speed'):
            return point.speed
        elif isinstance(point, dict):
            return point.get('speed', 0.0)
        return 0.0
    
    def _get_point_heading(self, point) -> float:
        """获取点的航向"""
        if hasattr(point, 'heading'):
            return point.heading
        elif isinstance(point, dict):
            return point.get('heading', 0.0)
        return 0.0
    
    def _calculate_heading_diff(self, heading1: float, heading2: float) -> float:
        """计算航向差异"""
        diff = abs(heading2 - heading1)
        if diff > 180:
            diff = 360 - diff
        return diff
