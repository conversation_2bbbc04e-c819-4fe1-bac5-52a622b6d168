# Config.json 字段详细解释文档

## 概述
本文档详细解释config.json中每个字段的含义和作用，特别关注ID轨迹连接处的漏检分析和轨迹内部时间跳跃检测。

## 1. ROI空间过滤参数

### 1.1 roi_long (默认: 20.0)
- **含义**: ROI纵向范围，单位：米
- **作用**: 以RTK轨迹点为中心，沿行驶方向前后各扩展的距离
- **影响**: 值越大，保留的感知目标越多，但计算量增加；值越小，可能过滤掉有用目标
- **建议**: 根据感知设备精度调整，一般10-30米

### 1.2 roi_lat (默认: 5.0)
- **含义**: ROI横向范围，单位：米
- **作用**: 以RTK轨迹点为中心，垂直行驶方向左右各扩展的距离
- **影响**: 值越大，保留的感知目标越多；值越小，过滤更严格
- **建议**: 根据道路宽度和感知误差调整，一般3-8米

## 2. DTW匹配参数

### 2.1 win_sec (默认: 3.0)
- **含义**: DTW匹配时间窗口，单位：秒
- **作用**: 限制DTW算法的时间对齐范围，防止过度扭曲
- **影响**: 值越大，允许的时间偏移越大；值越小，要求时间对齐更严格
- **建议**: 根据系统时间延迟调整，一般1-5秒

### 2.2 local_match_thr (默认: 0.8)
- **含义**: 核心链匹配阈值
- **作用**: 轨迹段要进入核心链的最低匹配分数
- **影响**: 值越高，匹配越严格，可能遗漏真实匹配；值越低，可能引入错误匹配
- **建议**: 根据数据质量调整，高质量数据用0.8-0.9，低质量数据用0.6-0.7

### 2.3 split_match_thr (默认: 0.7)
- **含义**: 分裂检测阈值
- **作用**: 判断两个ID是否为同一车辆分裂的相似度阈值
- **影响**: 值越高，分裂检测越严格；值越低，更容易检测到分裂
- **建议**: 比local_match_thr稍低，一般0.6-0.8

### 2.4 dtw_radius (默认: 5)
- **含义**: DTW算法的搜索半径
- **作用**: 限制DTW路径搜索的范围，提高计算效率
- **影响**: 值越大，计算越慢但结果更准确；值越小，计算快但可能错过最优匹配
- **建议**: 根据序列长度调整，一般3-10

## 3. 时间段连接参数

### 3.1 overlap_min (默认: 0.5)
- **含义**: 最小重叠比例
- **作用**: 两个轨迹段时间重叠的最小比例，用于分裂检测
- **影响**: 值越高，要求重叠越多才认为是分裂；值越低，更容易检测分裂
- **建议**: 根据检测频率调整，一般0.3-0.7

### 3.2 max_gap (默认: 2.0)
- **含义**: 最大允许间隙，单位：秒
- **作用**: 两个轨迹段之间的最大时间间隙，超过则不考虑连接
- **影响**: 值越大，允许的间隙越大；值越小，连接更严格
- **建议**: 根据感知设备性能调整，一般1-5秒

## 4. 异常检测参数

### 4.1 switch_dt (默认: 2.0)
- **含义**: ID切换时间阈值，单位：秒
- **作用**: 两个不同ID轨迹段间隔小于此值时，检查是否为ID切换
- **影响**: 值越大，更容易检测到ID切换；值越小，检测更严格
- **建议**: 根据ID切换频率调整，一般1-3秒

### 4.2 switch_dist (默认: 10.0)
- **含义**: ID切换距离阈值，单位：米
- **作用**: ID切换时，前后位置距离的最大允许值
- **影响**: 值越大，允许的位置跳跃越大；值越小，要求位置连续性更好
- **建议**: 根据感知精度调整，一般5-20米

### 4.3 switch_speed (默认: 5.0)
- **含义**: ID切换速度差阈值，单位：m/s
- **作用**: ID切换时，前后速度差的最大允许值
- **影响**: 值越大，允许的速度跳跃越大；值越小，要求速度连续性更好
- **建议**: 根据车辆动力学调整，一般3-10 m/s

### 4.4 switch_heading (默认: 30.0)
- **含义**: ID切换航向差阈值，单位：度
- **作用**: ID切换时，前后航向差的最大允许值
- **影响**: 值越大，允许的航向跳跃越大；值越小，要求航向连续性更好
- **建议**: 根据道路几何调整，一般15-45度

## 5. 间隙填充参数

### 5.1 gap_match_thr (默认: 0.5)
- **含义**: 间隙填充匹配阈值
- **作用**: 填充时间间隙时的最低匹配分数要求
- **影响**: 值越高，填充越严格；值越低，更容易填充间隙
- **建议**: 比核心链阈值低，一般0.3-0.6

### 5.2 max_missing_gap (默认: 5.0)
- **含义**: 最大漏检间隙，单位：秒
- **作用**: 超过此时间的间隙被认为是长时间漏检，不尝试填充
- **影响**: 值越大，尝试填充更长的间隙；值越小，只填充短间隙
- **建议**: 根据应用场景调整，一般3-10秒

### 5.3 min_missing_gap (默认: 0.5)
- **含义**: 最小漏检间隙，单位：秒
- **作用**: 小于此时间的间隙不被认为是漏检，可能是正常的检测间隔
- **影响**: 值越大，更多间隙被忽略；值越小，更敏感地检测漏检
- **建议**: 根据检测频率调整，一般0.1-1秒

## 6. 输出控制参数

### 6.1 rtk_buffer (默认: 2.0)
- **含义**: RTK轨迹缓冲时间，单位：秒
- **作用**: 在匹配时间段前后扩展RTK轨迹的时间范围
- **影响**: 值越大，输出的RTK轨迹越长；值越小，输出更精确
- **建议**: 根据分析需求调整，一般1-5秒

### 6.2 good_match_thr (默认: 0.6)
- **含义**: 良好匹配阈值
- **作用**: 用于统计和评估匹配质量的阈值
- **影响**: 值越高，认为是良好匹配的标准越严格
- **建议**: 根据质量要求调整，一般0.5-0.8

## 7. 评分权重参数

### 7.1 peak_weight (默认: 0.6)
- **含义**: 峰值匹配权重
- **作用**: DTW匹配分数中峰值相似度的权重
- **影响**: 值越大，越重视最佳匹配点；值越小，更平衡考虑整体匹配
- **建议**: 一般0.4-0.8，根据数据特点调整

### 7.2 duration_weight (默认: 0.3)
- **含义**: 时长权重
- **作用**: 轨迹段时长在竞争选择中的权重
- **影响**: 值越大，越偏向选择长轨迹段；值越小，更重视匹配质量
- **建议**: 一般0.2-0.4，与peak_weight配合使用

### 7.3 stability_weight (默认: 0.1)
- **含义**: 稳定性权重
- **作用**: 轨迹稳定性在评分中的权重
- **影响**: 值越大，越偏向选择稳定的轨迹；值越小，更重视其他因素
- **建议**: 一般0.05-0.2，作为细调参数

### 7.4 min_segment_length (默认: 2)
- **含义**: 最小轨迹段长度
- **作用**: 过滤掉过短的轨迹段，单位：数据点数
- **影响**: 值越大，过滤越严格；值越小，保留更多短段
- **建议**: 根据数据频率调整，一般1-5个点

## 8. 关键问题分析

### 8.1 ID轨迹连接处的漏检分析

**重点关注参数**：
- `switch_dt`: 控制ID切换检测的时间窗口
- `switch_dist`, `switch_speed`, `switch_heading`: 四阈值检查确保连接合理性
- `gap_match_thr`: 控制间隙填充的质量要求

**分析重点**：
1. **连接处间隙**: 不同ID轨迹段之间的时间间隙
2. **运动连续性**: 位置、速度、航向的连续性检查
3. **填充质量**: 间隙填充的匹配分数评估

**建议配置**：
```json
{
  "switch_dt": 2.0,        // 允许2秒内的ID切换
  "switch_dist": 10.0,     // 位置跳跃不超过10米
  "switch_speed": 5.0,     // 速度变化不超过5m/s
  "switch_heading": 30.0,  // 航向变化不超过30度
  "gap_match_thr": 0.5     // 间隙填充要求0.5以上匹配分数
}
```

### 8.2 轨迹内部时间跳跃检测

**相关参数**：
- `min_missing_gap`: 定义最小漏检间隙
- `max_missing_gap`: 定义最大漏检间隙
- `win_sec`: DTW时间窗口，影响时间对齐检测

**检测方法**：
1. **时间间隔分析**: 检查同一ID内相邻点的时间间隔
2. **异常间隔标记**: 超过正常检测频率的间隔
3. **内部一致性**: 检查轨迹段内部的时间单调性

**建议配置**：
```json
{
  "min_missing_gap": 0.5,   // 0.5秒以上认为是异常间隔
  "max_missing_gap": 5.0,   // 5秒以上认为是长时间漏检
  "win_sec": 3.0           // 3秒时间窗口检测时间对齐
}
```

## 9. 配置优化建议

### 9.1 高精度场景配置
```json
{
  "local_match_thr": 0.85,
  "split_match_thr": 0.75,
  "switch_dist": 8.0,
  "gap_match_thr": 0.6
}
```

### 9.2 低质量数据配置
```json
{
  "local_match_thr": 0.65,
  "split_match_thr": 0.55,
  "switch_dist": 15.0,
  "gap_match_thr": 0.4
}
```

### 9.3 实时处理配置
```json
{
  "dtw_radius": 3,
  "win_sec": 2.0,
  "max_gap": 1.5
}
```

## 10. 调试建议

1. **逐步调整**: 先调整核心参数（local_match_thr），再调整细节参数
2. **数据驱动**: 根据实际数据质量调整参数
3. **平衡考虑**: 在准确性和召回率之间找到平衡
4. **场景适配**: 不同场景使用不同配置文件
5. **日志分析**: 通过详细日志分析参数效果 