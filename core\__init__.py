"""
车路协同轨迹匹配工具 - 核心模块

本模块包含轨迹匹配的核心功能：
- data_utils: 数据处理工具
- simple_distance_matcher: 简单距离匹配算法
- trajectory_matcher: 轨迹匹配主程序
- output_generator: 输出生成器
- gap_analyzer: 间隙分析器
- preprocessor: 数据预处理器
"""

from .data_utils import *
from .simple_distance_matcher import *
from .trajectory_matcher import *
from .output_generator import *

__version__ = "1.0.0"
__author__ = "Trajectory Matching Team"
__all__ = [
    "DataUtils",
    "SimpleDistanceMatcher",
    "TrajectoryMatcher",
    "OutputGenerator"
]