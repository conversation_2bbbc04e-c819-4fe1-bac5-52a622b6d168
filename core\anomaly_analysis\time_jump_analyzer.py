"""
时间跳跃分析器
检测轨迹数据中的时间异常和跳跃
"""

from typing import List, Dict, Any
import logging
from datetime import datetime, timedelta

from .base_analyzer import BaseAnomalyAnalyzer, AnalysisResult

logger = logging.getLogger(__name__)


class TimeJumpAnalyzer(BaseAnomalyAnalyzer):
    """时间跳跃分析器"""
    
    def __init__(self, config: Dict[str, Any], name: str = None):
        super().__init__(config, name or 'TimeJumpAnalyzer')
        
        # 时间跳跃检测阈值
        self.max_time_gap = config.get('max_time_gap', 2.0)  # 最大时间间隔(秒)
        self.min_time_interval = config.get('min_time_interval', 0.05)  # 最小时间间隔(秒)
        self.backward_time_tolerance = config.get('backward_time_tolerance', 0.1)  # 时间倒退容忍度(秒)
        self.enable_internal_jump_detection = config.get('enable_internal_jump_detection', True)
        
        self.logger.info(f"时间跳跃分析器初始化: 最大间隔={self.max_time_gap}s, "
                        f"最小间隔={self.min_time_interval}s")
    
    @property
    def analysis_type(self) -> str:
        return "time_jump"
    
    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        """
        执行时间跳跃分析
        
        Args:
            trajectory_chain: 轨迹链数据
            **kwargs: 其他参数
            
        Returns:
            AnalysisResult: 分析结果
        """
        self.log_analysis_start(trajectory_chain, **kwargs)
        
        if not self.validate_input(trajectory_chain, **kwargs):
            return self.create_result(False, "输入数据验证失败")
        
        try:
            result = self.create_result()
            
            # 检测段间时间跳跃
            inter_segment_jumps = self._detect_inter_segment_jumps(trajectory_chain)
            
            # 检测段内时间跳跃
            intra_segment_jumps = []
            if self.enable_internal_jump_detection:
                intra_segment_jumps = self._detect_intra_segment_jumps(trajectory_chain)
            
            # 合并所有跳跃事件
            all_jumps = inter_segment_jumps + intra_segment_jumps
            
            # 添加事件到结果
            for jump in all_jumps:
                result.add_event(jump)
            
            # 添加统计信息
            result.add_statistic('total_time_jumps', len(all_jumps))
            result.add_statistic('inter_segment_jumps', len(inter_segment_jumps))
            result.add_statistic('intra_segment_jumps', len(intra_segment_jumps))
            result.add_statistic('trajectory_segments', len(trajectory_chain))
            
            # 按跳跃类型统计
            jump_types = {}
            severity_counts = {'low': 0, 'medium': 0, 'high': 0}
            
            for jump in all_jumps:
                jump_type = jump.get('jump_type', 'unknown')
                jump_types[jump_type] = jump_types.get(jump_type, 0) + 1
                
                severity = jump.get('severity', 'medium')
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            result.add_statistic('jump_types', jump_types)
            result.add_statistic('severity_distribution', severity_counts)
            
            # 添加元数据
            result.add_metadata('analysis_method', 'time_sequence_analysis')
            result.add_metadata('detection_thresholds', {
                'max_time_gap': self.max_time_gap,
                'min_time_interval': self.min_time_interval,
                'backward_time_tolerance': self.backward_time_tolerance
            })
            result.add_metadata('internal_detection_enabled', self.enable_internal_jump_detection)
            
            self.log_analysis_complete(result)
            return result
            
        except Exception as e:
            error_msg = f"时间跳跃分析失败: {str(e)}"
            self.logger.error(error_msg)
            return self.create_result(False, error_msg)
    
    def _detect_inter_segment_jumps(self, trajectory_chain: List[Any]) -> List[Dict[str, Any]]:
        """
        检测段间时间跳跃
        
        Args:
            trajectory_chain: 轨迹链
            
        Returns:
            List[Dict[str, Any]]: 段间跳跃事件列表
        """
        jumps = []
        
        if len(trajectory_chain) < 2:
            return jumps
        
        # 按时间排序
        sorted_chain = sorted(trajectory_chain, key=lambda x: x.start_time)
        
        for i in range(len(sorted_chain) - 1):
            prev_seg = sorted_chain[i]
            next_seg = sorted_chain[i + 1]
            
            # 计算时间间隔
            time_gap = (next_seg.start_time - prev_seg.end_time).total_seconds()
            
            # 检测异常时间间隔
            if time_gap > self.max_time_gap:
                jumps.append({
                    'type': 'time_jump',
                    'jump_type': 'large_gap',
                    'timestamp': next_seg.start_time,
                    'severity': self._classify_gap_severity(time_gap),
                    'time_gap': time_gap,
                    'prev_segment_id': prev_seg.id,
                    'next_segment_id': next_seg.id,
                    'prev_end_time': prev_seg.end_time,
                    'next_start_time': next_seg.start_time,
                    'description': f'段间时间间隔过大: {time_gap:.2f}s',
                    'analyzer': self.name
                })
            
            elif time_gap < -self.backward_time_tolerance:
                jumps.append({
                    'type': 'time_jump',
                    'jump_type': 'time_overlap',
                    'timestamp': next_seg.start_time,
                    'severity': 'high',
                    'time_gap': time_gap,
                    'prev_segment_id': prev_seg.id,
                    'next_segment_id': next_seg.id,
                    'prev_end_time': prev_seg.end_time,
                    'next_start_time': next_seg.start_time,
                    'description': f'时间重叠或倒退: {time_gap:.2f}s',
                    'analyzer': self.name
                })
        
        return jumps
    
    def _detect_intra_segment_jumps(self, trajectory_chain: List[Any]) -> List[Dict[str, Any]]:
        """
        检测段内时间跳跃
        
        Args:
            trajectory_chain: 轨迹链
            
        Returns:
            List[Dict[str, Any]]: 段内跳跃事件列表
        """
        jumps = []
        
        for segment in trajectory_chain:
            if not hasattr(segment, 'points') or len(segment.points) < 2:
                continue
            
            segment_jumps = self._analyze_segment_time_sequence(segment)
            jumps.extend(segment_jumps)
        
        return jumps
    
    def _analyze_segment_time_sequence(self, segment) -> List[Dict[str, Any]]:
        """分析段内时间序列"""
        jumps = []
        points = segment.points
        
        for i in range(1, len(points)):
            prev_point = points[i-1]
            curr_point = points[i]
            
            # 获取时间戳
            prev_time = self._get_point_timestamp(prev_point)
            curr_time = self._get_point_timestamp(curr_point)
            
            if not prev_time or not curr_time:
                continue
            
            # 计算时间间隔
            time_diff = (curr_time - prev_time).total_seconds()
            
            # 检测异常时间间隔
            if time_diff > self.max_time_gap:
                jumps.append({
                    'type': 'time_jump',
                    'jump_type': 'internal_large_gap',
                    'timestamp': curr_time,
                    'severity': self._classify_gap_severity(time_diff),
                    'segment_id': segment.id,
                    'time_gap': time_diff,
                    'point_index': i,
                    'prev_timestamp': prev_time,
                    'curr_timestamp': curr_time,
                    'description': f'段内时间跳跃: {time_diff:.2f}s',
                    'analyzer': self.name
                })
            
            elif time_diff < self.min_time_interval and time_diff > 0:
                jumps.append({
                    'type': 'time_jump',
                    'jump_type': 'too_frequent',
                    'timestamp': curr_time,
                    'severity': 'low',
                    'segment_id': segment.id,
                    'time_gap': time_diff,
                    'point_index': i,
                    'prev_timestamp': prev_time,
                    'curr_timestamp': curr_time,
                    'description': f'采样频率过高: {time_diff:.3f}s',
                    'analyzer': self.name
                })
            
            elif time_diff <= 0:
                jumps.append({
                    'type': 'time_jump',
                    'jump_type': 'time_backward',
                    'timestamp': curr_time,
                    'severity': 'high',
                    'segment_id': segment.id,
                    'time_gap': time_diff,
                    'point_index': i,
                    'prev_timestamp': prev_time,
                    'curr_timestamp': curr_time,
                    'description': f'时间倒退: {time_diff:.3f}s',
                    'analyzer': self.name
                })
        
        return jumps
    
    def _get_point_timestamp(self, point):
        """获取点的时间戳"""
        if hasattr(point, 'timestamp'):
            return point.timestamp
        elif isinstance(point, dict):
            timestamp = point.get('timestamp')
            if isinstance(timestamp, str):
                try:
                    return datetime.fromisoformat(timestamp)
                except ValueError:
                    return None
            return timestamp
        return None
    
    def _classify_gap_severity(self, time_gap: float) -> str:
        """分类时间间隔的严重程度"""
        if time_gap > 10.0:
            return 'high'
        elif time_gap > 5.0:
            return 'medium'
        else:
            return 'low'
    
    def generate_time_analysis_report(self, result: AnalysisResult) -> Dict[str, Any]:
        """生成时间分析报告"""
        if not result.success:
            return {'error': result.error_message}
        
        report = {
            'summary': {
                'total_jumps': result.get_statistic('total_time_jumps', 0),
                'inter_segment_jumps': result.get_statistic('inter_segment_jumps', 0),
                'intra_segment_jumps': result.get_statistic('intra_segment_jumps', 0)
            },
            'jump_types': result.get_statistic('jump_types', {}),
            'severity_distribution': result.get_statistic('severity_distribution', {}),
            'recommendations': []
        }
        
        # 生成建议
        total_jumps = report['summary']['total_jumps']
        if total_jumps > 0:
            if report['summary']['inter_segment_jumps'] > 0:
                report['recommendations'].append(
                    "检测到段间时间跳跃，建议检查轨迹匹配算法的时间连续性处理"
                )
            
            if report['summary']['intra_segment_jumps'] > 0:
                report['recommendations'].append(
                    "检测到段内时间跳跃，建议检查原始数据的时间戳质量"
                )
            
            severity_dist = report['severity_distribution']
            if severity_dist.get('high', 0) > 0:
                report['recommendations'].append(
                    "存在高严重程度的时间异常，需要优先处理"
                )
        
        return report
