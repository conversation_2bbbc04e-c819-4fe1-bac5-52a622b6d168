"""
测试精度分析报告集成
"""

import os
import sys
import json
import tempfile
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.anomaly_analysis.accuracy_analyzer import AccuracyAnalyzer
from core.anomaly_analysis.result_summarizer import ResultSummarizer
from core.output_generator import OutputGenerator
from core.report_generator import AccuracyReportGenerator
from core.data_utils import RTKPoint
from core.data_utils import PerceptionPoint
from core.simple_distance_matcher import TrajectorySegment


def create_test_data():
    """创建测试数据"""
    # 创建RTK测试数据
    rtk_points = []
    base_time = datetime.now()
    base_lat, base_lon = 39.9042, 116.4074  # 北京坐标
    
    for i in range(50):
        timestamp = base_time + timedelta(seconds=i * 0.1)
        lat = base_lat + i * 0.0001
        lon = base_lon + i * 0.0001
        speed = 10.0 + i * 0.1
        heading = 90.0 + i * 0.5
        
        rtk_points.append(RTKPoint(
            timestamp=timestamp,
            lat=lat,
            lon=lon,
            speed=speed,
            heading=heading
        ))
    
    # 创建感知数据（带有一些误差）
    perception_points = []
    for i in range(50):
        timestamp = base_time + timedelta(seconds=i * 0.1)
        # 添加一些随机误差
        lat_error = (i % 10 - 5) * 0.00001  # ±5个单位的误差
        lon_error = (i % 8 - 4) * 0.00001   # ±4个单位的误差
        speed_error = (i % 6 - 3) * 0.2     # ±0.6 m/s的误差
        heading_error = (i % 12 - 6) * 2    # ±12度的误差
        
        lat = base_lat + i * 0.0001 + lat_error
        lon = base_lon + i * 0.0001 + lon_error
        speed = 10.0 + i * 0.1 + speed_error
        heading = 90.0 + i * 0.5 + heading_error
        
        perception_points.append(PerceptionPoint(
            timestamp=timestamp,
            id=f"obj_{i}",
            lat=lat,
            lon=lon,
            speed=speed,
            heading=heading
        ))
    
    # 创建轨迹段
    trajectory_segment = TrajectorySegment(
        id="test_segment",
        points=perception_points,
        start_time=perception_points[0].timestamp,
        end_time=perception_points[-1].timestamp
    )
    
    return rtk_points, [trajectory_segment]


def test_accuracy_analyzer():
    """测试精度分析器"""
    print("🧪 测试精度分析器...")
    
    # 创建测试数据
    rtk_points, matched_chain = create_test_data()
    
    # 配置精度分析器
    config = {
        'max_time_gap': 2.0,
        'position_error_thresholds': [1.0, 2.0, 5.0],
        'speed_error_thresholds': [1.0, 2.0, 5.0],
        'heading_error_thresholds': [5.0, 10.0, 30.0]
    }
    
    # 创建分析器
    analyzer = AccuracyAnalyzer(config)
    
    # 执行分析
    result = analyzer.analyze(matched_chain, rtk_points=rtk_points)
    
    print(f"  ✅ 分析成功: {result.success}")
    print(f"  📊 样本数量: {result.metadata.get('samples_count', 0)}")
    print(f"  ⚠️  异常事件: {len(result.events)}")
    
    return result


def test_result_summarizer(accuracy_result):
    """测试结果汇总器"""
    print("\n🧪 测试结果汇总器...")
    
    # 创建汇总器
    config = {}  # 空配置
    summarizer = ResultSummarizer(config)
    
    # 模拟分析结果字典
    analysis_results = {
        'accuracy': accuracy_result
    }
    
    # 生成汇总
    summary = summarizer.summarize(analysis_results)
    
    print(f"  ✅ 汇总成功")
    print(f"  📊 分析器数量: {summary.get('total_analyzers', 0)}")
    
    # 检查精度分析汇总
    detailed = summary.get('detailed_analysis', {})
    accuracy_summary = detailed.get('accuracy_analysis', {})
    
    if accuracy_summary:
        print(f"  🎯 精度分析汇总:")
        print(f"    - 总样本: {accuracy_summary.get('total_samples', 0)}")
        print(f"    - 分析时长: {accuracy_summary.get('analysis_duration', 0)}s")
        
        pos_acc = accuracy_summary.get('position_accuracy', {})
        if pos_acc:
            print(f"    - 定位平均误差: {pos_acc.get('mean_error_m', 0):.3f}m")
        
        quality = accuracy_summary.get('quality_assessment', {})
        if quality:
            print(f"    - 综合质量: {quality.get('overall_quality', 'unknown')}")
    
    return summary


def test_output_generator(accuracy_result, rtk_points, matched_chain):
    """测试输出生成器"""
    print("\n🧪 测试输出生成器...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建输出生成器
        class MockParams:
            def __init__(self):
                self.max_gap = 2.0
                self.min_segment_length = 3

        params = MockParams()
        generator = OutputGenerator(params)
        
        # 模拟异常数据
        anomalies = {
            'split_events': [],
            'id_switches': [],
            'missing_gaps': []
        }
        
        # 模拟分析结果
        analysis_results = {
            'accuracy': accuracy_result
        }
        
        # 生成CSV报告
        csv_path = os.path.join(temp_dir, 'test_accuracy_report.csv')
        generator.generate_matched_csv(
            rtk_points, matched_chain, csv_path, anomalies, analysis_results
        )
        
        # 检查CSV文件
        if os.path.exists(csv_path):
            print(f"  ✅ CSV报告生成成功: {csv_path}")
            
            # 读取并检查CSV内容
            with open(csv_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"    - CSV行数: {len(lines)}")
                
                # 检查表头是否包含精度字段
                header = lines[0].strip()
                accuracy_fields = ['position_error_m', 'speed_abs_error_ms', 'heading_error_deg']
                has_accuracy_fields = all(field in header for field in accuracy_fields)
                print(f"    - 包含精度字段: {has_accuracy_fields}")
        
        # 生成JSON报告
        json_path = os.path.join(temp_dir, 'test_accuracy_report.json')
        generator.generate_diagnostic_json(
            anomalies, json_path, matched_chain, rtk_points, analysis_results
        )
        
        # 检查JSON文件
        if os.path.exists(json_path):
            print(f"  ✅ JSON报告生成成功: {json_path}")
            
            # 读取并检查JSON内容
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                has_accuracy_analysis = 'accuracy_analysis' in data
                print(f"    - 包含精度分析: {has_accuracy_analysis}")
                
                if has_accuracy_analysis:
                    acc_data = data['accuracy_analysis']
                    print(f"    - 精度分析样本数: {acc_data.get('analysis_info', {}).get('total_samples', 0)}")


def test_html_report_generator(accuracy_result):
    """测试HTML报告生成器"""
    print("\n🧪 测试HTML报告生成器...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建报告生成器
        report_generator = AccuracyReportGenerator()
        
        # 生成HTML报告
        html_path = os.path.join(temp_dir, 'test_accuracy_report.html')
        success = report_generator.generate_html_report(accuracy_result, html_path)
        
        if success and os.path.exists(html_path):
            print(f"  ✅ HTML报告生成成功: {html_path}")
            
            # 检查HTML文件大小
            file_size = os.path.getsize(html_path)
            print(f"    - 文件大小: {file_size} bytes")
            
            # 检查HTML内容
            with open(html_path, 'r', encoding='utf-8') as f:
                content = f.read()
                has_title = '精度分析报告' in content
                has_metrics = '定位精度分析' in content and '速度精度分析' in content
                print(f"    - 包含标题: {has_title}")
                print(f"    - 包含指标: {has_metrics}")
        else:
            print(f"  ❌ HTML报告生成失败")


def test_integration():
    """集成测试"""
    print("🚀 开始精度分析报告集成测试\n")
    
    try:
        # 1. 测试精度分析器
        accuracy_result = test_accuracy_analyzer()
        
        if not accuracy_result.success:
            print("❌ 精度分析器测试失败")
            return False
        
        # 2. 测试结果汇总器
        summary = test_result_summarizer(accuracy_result)
        
        # 3. 测试输出生成器
        rtk_points, matched_chain = create_test_data()
        test_output_generator(accuracy_result, rtk_points, matched_chain)
        
        # 4. 测试HTML报告生成器
        test_html_report_generator(accuracy_result)
        
        print("\n✅ 所有测试通过！精度分析报告集成成功")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_integration()
    sys.exit(0 if success else 1)
