"""
异常分析管理器
统一管理所有异常分析器，提供插件式扩展能力
"""

from typing import List, Dict, Any, Optional, Type
import logging
from datetime import datetime

from .base_analyzer import BaseAnomalyAnalyzer, AnalysisResult, analyzer_registry
from .result_summarizer import ResultSummarizer

logger = logging.getLogger(__name__)


class AnomalyAnalysisManager:
    """异常分析管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化异常分析管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.analyzers: Dict[str, BaseAnomalyAnalyzer] = {}
        self.result_summarizer = ResultSummarizer(config)
        self.analysis_history: List[Dict[str, Any]] = []
        
        # 自动注册内置分析器
        self._register_builtin_analyzers()
        
        # 根据配置初始化分析器
        self._initialize_analyzers()
        
        logger.info(f"异常分析管理器初始化完成，已加载 {len(self.analyzers)} 个分析器")
    
    def _register_builtin_analyzers(self):
        """注册内置分析器"""
        try:
            from .split_detector import SplitDetectionAnalyzer
            from .id_switch_analyzer import IDSwitchAnalyzer
            from .missing_gap_analyzer import MissingGapAnalyzer
            from .accuracy_analyzer import AccuracyAnalyzer

            analyzer_registry.register(SplitDetectionAnalyzer, 'split_detection')
            analyzer_registry.register(IDSwitchAnalyzer, 'id_switch')
            analyzer_registry.register(MissingGapAnalyzer, 'missing_gap')
            analyzer_registry.register(AccuracyAnalyzer, 'accuracy')

            # 尝试注册扩展分析器
            try:
                from .trajectory_quality_analyzer import TrajectoryQualityAnalyzer
                from .time_jump_analyzer import TimeJumpAnalyzer

                analyzer_registry.register(TrajectoryQualityAnalyzer, 'trajectory_quality')
                analyzer_registry.register(TimeJumpAnalyzer, 'time_jump')

                logger.info("扩展分析器注册完成")
            except ImportError:
                logger.info("扩展分析器不可用，跳过注册")

            logger.info("内置分析器注册完成")
        except ImportError as e:
            logger.warning(f"部分内置分析器注册失败: {e}")
    
    def _initialize_analyzers(self):
        """根据配置初始化分析器"""
        # 获取启用的分析器列表
        enabled_analyzers = self.config.get('enabled_analyzers', [
            'split_detection', 'id_switch', 'missing_gap'
        ])
        
        for analyzer_name in enabled_analyzers:
            try:
                analyzer = analyzer_registry.create_analyzer(analyzer_name, self.config)
                if analyzer and analyzer.is_enabled():
                    self.analyzers[analyzer_name] = analyzer
                    logger.info(f"分析器 {analyzer_name} 初始化成功")
                else:
                    logger.warning(f"分析器 {analyzer_name} 初始化失败或被禁用")
            except Exception as e:
                logger.error(f"初始化分析器 {analyzer_name} 时出错: {e}")
    
    def register_analyzer(self, analyzer_class: Type[BaseAnomalyAnalyzer], 
                         name: str = None, auto_initialize: bool = True):
        """
        注册新的分析器
        
        Args:
            analyzer_class: 分析器类
            name: 分析器名称
            auto_initialize: 是否自动初始化
        """
        analyzer_name = name or analyzer_class.__name__
        analyzer_registry.register(analyzer_class, analyzer_name)
        
        if auto_initialize:
            try:
                analyzer = analyzer_class(self.config, analyzer_name)
                if analyzer.is_enabled():
                    self.analyzers[analyzer_name] = analyzer
                    logger.info(f"分析器 {analyzer_name} 注册并初始化成功")
            except Exception as e:
                logger.error(f"自动初始化分析器 {analyzer_name} 失败: {e}")
    
    def add_analyzer(self, analyzer: BaseAnomalyAnalyzer):
        """
        添加分析器实例
        
        Args:
            analyzer: 分析器实例
        """
        self.analyzers[analyzer.name] = analyzer
        logger.info(f"添加分析器实例: {analyzer.name}")
    
    def remove_analyzer(self, name: str):
        """
        移除分析器
        
        Args:
            name: 分析器名称
        """
        if name in self.analyzers:
            del self.analyzers[name]
            logger.info(f"移除分析器: {name}")
    
    def get_analyzer(self, name: str) -> Optional[BaseAnomalyAnalyzer]:
        """
        获取分析器
        
        Args:
            name: 分析器名称
            
        Returns:
            BaseAnomalyAnalyzer: 分析器实例
        """
        return self.analyzers.get(name)
    
    def list_analyzers(self) -> List[str]:
        """列出所有分析器名称"""
        return list(self.analyzers.keys())
    
    def analyze_all(self, trajectory_chain: List[Any], **kwargs) -> Dict[str, AnalysisResult]:
        """
        执行所有启用的分析器
        
        Args:
            trajectory_chain: 轨迹链数据
            **kwargs: 其他分析参数
            
        Returns:
            Dict[str, AnalysisResult]: 所有分析结果
        """
        results = {}
        analysis_start_time = datetime.now()
        
        logger.info(f"开始执行异常分析，共 {len(self.analyzers)} 个分析器")
        
        for name, analyzer in self.analyzers.items():
            if not analyzer.is_enabled():
                logger.info(f"跳过已禁用的分析器: {name}")
                continue
            
            try:
                logger.info(f"执行分析器: {name}")
                result = analyzer.analyze(trajectory_chain, **kwargs)
                results[name] = result
                
                if result.success:
                    logger.info(f"分析器 {name} 执行成功，发现 {result.get_event_count()} 个事件")
                else:
                    logger.warning(f"分析器 {name} 执行失败: {result.error_message}")
                    
            except Exception as e:
                logger.error(f"分析器 {name} 执行异常: {e}")
                error_result = AnalysisResult(
                    analyzer_name=name,
                    analysis_type="error",
                    success=False,
                    error_message=str(e)
                )
                results[name] = error_result
        
        analysis_duration = (datetime.now() - analysis_start_time).total_seconds()
        logger.info(f"异常分析完成，耗时 {analysis_duration:.2f} 秒")
        
        # 记录分析历史
        self._record_analysis_history(results, analysis_duration, **kwargs)
        
        return results
    
    def analyze_single(self, analyzer_name: str, trajectory_chain: List[Any], 
                      **kwargs) -> Optional[AnalysisResult]:
        """
        执行单个分析器
        
        Args:
            analyzer_name: 分析器名称
            trajectory_chain: 轨迹链数据
            **kwargs: 其他分析参数
            
        Returns:
            AnalysisResult: 分析结果
        """
        analyzer = self.get_analyzer(analyzer_name)
        if not analyzer:
            logger.error(f"分析器 {analyzer_name} 不存在")
            return None
        
        if not analyzer.is_enabled():
            logger.warning(f"分析器 {analyzer_name} 已禁用")
            return None
        
        try:
            return analyzer.analyze(trajectory_chain, **kwargs)
        except Exception as e:
            logger.error(f"执行分析器 {analyzer_name} 时出错: {e}")
            return AnalysisResult(
                analyzer_name=analyzer_name,
                analysis_type="error",
                success=False,
                error_message=str(e)
            )
    
    def generate_summary(self, results: Dict[str, AnalysisResult]) -> Dict[str, Any]:
        """
        生成分析结果摘要
        
        Args:
            results: 分析结果字典
            
        Returns:
            Dict[str, Any]: 摘要信息
        """
        return self.result_summarizer.summarize(results)
    
    def _record_analysis_history(self, results: Dict[str, AnalysisResult], 
                                duration: float, **kwargs):
        """记录分析历史"""
        history_entry = {
            'timestamp': datetime.now(),
            'duration': duration,
            'analyzers_count': len(results),
            'total_events': sum(r.get_event_count() for r in results.values()),
            'success_count': sum(1 for r in results.values() if r.success),
            'kwargs': kwargs
        }
        
        self.analysis_history.append(history_entry)
        
        # 保持历史记录数量限制
        max_history = self.config.get('max_analysis_history', 100)
        if len(self.analysis_history) > max_history:
            self.analysis_history = self.analysis_history[-max_history:]
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        if not self.analysis_history:
            return {}
        
        total_analyses = len(self.analysis_history)
        total_duration = sum(h['duration'] for h in self.analysis_history)
        total_events = sum(h['total_events'] for h in self.analysis_history)
        avg_duration = total_duration / total_analyses if total_analyses > 0 else 0
        
        return {
            'total_analyses': total_analyses,
            'total_duration': total_duration,
            'average_duration': avg_duration,
            'total_events_detected': total_events,
            'analyzer_count': len(self.analyzers)
        }
