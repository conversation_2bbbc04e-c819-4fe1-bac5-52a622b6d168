# 统一评分系统使用指南

## 📖 概述

统一评分系统是车路协同轨迹匹配工具的核心创新，旨在解决传统评分方法中的**短轨迹歧视**和**长轨迹拖累**问题。通过智能策略选择，为不同特征的轨迹提供最公平、最准确的评分。

## 🎯 核心问题与解决方案

### 问题1: 短轨迹歧视
**现象**: 高质量的短轨迹（如5秒完美匹配）因为时长不足被传统评分方法低估，无法通过0.7阈值。

**原因**: 传统评分中时长权重过高，短轨迹在duration_ratio上天然劣势。

**解决**: 
- 使用`short_trajectory`策略
- 主要基于空间质量评分，弱化时长影响
- 短轨迹阈值可配置（默认10秒）

### 问题2: 长轨迹被拖累
**现象**: 长轨迹中有优秀的局部段，但被低质量的尾部拖累，整体分数偏低。

**原因**: 传统评分使用全局平均，无法识别局部优势。

**解决**:
- 使用`peak_quality_priority`策略：识别最佳5秒窗口
- 使用`segment_quality_priority`策略：基于高质量段占比评分
- 避免被低质量段拖累

## 🔧 评分策略详解

### 1. 短轨迹策略 (short_trajectory)

**适用条件**: 轨迹时长 ≤ `short_trajectory_threshold`（默认10秒）

**评分逻辑**:
```python
# 主要基于空间质量，弱化时长影响
score = spatial_quality * 0.8 + temporal_quality * 0.2
```

**配置参数**:
```json
{
  "short_trajectory_threshold": 10.0,    // 短轨迹时长阈值(秒)
  "spatial_decay_distance": 5.0         // 空间衰减距离(米)
}
```

**使用场景**:
- ID切换导致的短轨迹段
- 检测间断造成的轨迹分割
- 高质量但时长不足的轨迹

### 2. 峰值优先策略 (peak_quality_priority)

**适用条件**: 
- 长轨迹（>10秒）
- 峰值质量 ≥ `quality_threshold_high`（默认0.8）

**评分逻辑**:
```python
# 寻找最佳5秒窗口，基于峰值质量评分
peak_score = find_best_window_quality(trajectory, window_size=5.0)
score = peak_score * 0.9 + overall_quality * 0.1
```

**配置参数**:
```json
{
  "peak_window_duration": 5.0,          // 峰值窗口时长(秒)
  "quality_threshold_high": 0.8         // 高质量阈值
}
```

**使用场景**:
- 轨迹中有明显的高质量段
- 前好后差或中间优秀的轨迹
- 需要识别局部优势的场景

### 3. 分段优先策略 (segment_quality_priority)

**适用条件**:
- 长轨迹（>10秒）
- 有多个高质量段
- 高质量段覆盖率 ≥ 30%

**评分逻辑**:
```python
# 基于高质量段的数量和覆盖率评分
high_quality_segments = count_high_quality_segments(trajectory)
coverage_ratio = high_quality_duration / total_duration
score = (high_quality_segments * 0.3 + coverage_ratio * 0.7) * base_quality
```

**配置参数**:
```json
{
  "segment_duration": 5.0,              // 分段时长(秒)
  "quality_threshold_medium": 0.6       // 中等质量阈值
}
```

**使用场景**:
- 质量分布不均的长轨迹
- 多段式高质量轨迹
- 需要综合评估多个优秀段的场景

### 4. 传统长轨迹策略 (traditional_long_trajectory)

**适用条件**: 其他长轨迹（兜底策略）

**评分逻辑**:
```python
# 兼容传统评分逻辑
score = peak_score * 0.6 + duration_ratio * 0.3 + stability_score * 0.1
```

**使用场景**:
- 质量平均的长轨迹
- 无明显局部优势的轨迹
- 保持与历史系统的兼容性

## 📊 配置文件详解

### 完整配置示例
```json
{
  "scoring": {
    "method": "unified",                   // 启用统一评分
    "local_match_thr": 0.7,               // 匹配阈值
    
    // 策略选择参数
    "short_trajectory_threshold": 10.0,    // 短轨迹阈值(秒)
    "quality_threshold_high": 0.8,         // 高质量阈值
    "quality_threshold_medium": 0.6,       // 中等质量阈值
    
    // 窗口和分段参数
    "peak_window_duration": 5.0,           // 峰值窗口时长(秒)
    "segment_duration": 5.0,               // 分段时长(秒)
    
    // 质量计算参数
    "sampling_rate": 10.0,                 // 采样率(Hz)
    "spatial_decay_distance": 5.0          // 空间衰减距离(米)
  }
}
```

### 参数调优指南

#### 🎯 阈值参数
- **local_match_thr**: 
  - 范围: 0.5-0.9
  - 推荐: 0.7（平衡精度和召回率）
  - 过高: 拒绝好轨迹，过低: 接受差轨迹

- **quality_threshold_high**:
  - 范围: 0.7-0.9
  - 推荐: 0.8（识别真正的高质量段）
  - 影响峰值优先策略的触发

- **short_trajectory_threshold**:
  - 范围: 5.0-15.0秒
  - 推荐: 10.0秒（基于实际ID切换频率）
  - 影响短轨迹策略的适用范围

#### ⏱️ 时间参数
- **peak_window_duration**:
  - 范围: 3.0-10.0秒
  - 推荐: 5.0秒（平衡局部性和稳定性）
  - 过小: 过于局部，过大: 失去峰值意义

- **segment_duration**:
  - 范围: 3.0-10.0秒
  - 推荐: 5.0秒（与峰值窗口一致）
  - 影响分段质量评估的粒度

#### 🌍 空间参数
- **spatial_decay_distance**:
  - 范围: 3.0-10.0米
  - 推荐: 5.0米（基于GPS精度）
  - 影响距离对评分的衰减程度

## 🚀 使用示例

### 基本使用
```bash
# 启用统一评分
python main.py --rtk data/rtk.csv --perception data/perception.csv --config config/unified_config.json --verbose
```

### 参数调优
```bash
# 降低匹配阈值，提高召回率
# 修改config/unified_config.json中的local_match_thr为0.6

# 调整短轨迹阈值，适应不同场景
# 修改short_trajectory_threshold为8.0（更严格）或12.0（更宽松）
```

### 效果对比
```bash
# 对比不同评分方法
python test_realistic_unified_scoring.py

# 查看详细评分过程
python main.py --config config/unified_config.json --verbose
```

## 📈 性能监控

### 关键指标
1. **策略分布**: 各策略的使用频率
2. **分数分布**: 不同策略下的分数分布
3. **通过率**: 各时长段的轨迹通过率
4. **质量指标**: 峰值质量、平均质量、覆盖率

### 日志分析
```bash
# 查看策略选择日志
grep "策略=" output.log

# 统计各策略使用次数
grep -c "short_trajectory\|peak_quality_priority\|segment_quality_priority\|traditional_long_trajectory" output.log
```

## 🔍 故障诊断

### 常见问题

#### 1. 所有轨迹都使用traditional策略
**原因**: 质量阈值设置过高
**解决**: 降低`quality_threshold_high`到0.7

#### 2. 短轨迹分数仍然偏低
**原因**: `spatial_decay_distance`设置过小
**解决**: 增加到8.0-10.0米

#### 3. 长轨迹无法识别局部优势
**原因**: `peak_window_duration`设置不当
**解决**: 调整到3.0-7.0秒范围

#### 4. 评分计算错误
**原因**: 距离计算方法错误
**解决**: 确保使用`haversine_distance`方法

## 🎯 最佳实践

1. **渐进式部署**: 先在测试环境验证效果
2. **参数调优**: 基于实际数据调整参数
3. **效果监控**: 定期分析评分分布和策略使用情况
4. **A/B测试**: 对比统一评分与传统方法的效果
5. **文档记录**: 记录参数调整的原因和效果

通过合理配置和使用统一评分系统，可以显著提升轨迹匹配的准确性和公平性！
