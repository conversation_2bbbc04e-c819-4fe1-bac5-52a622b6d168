"""
异常分析模块
提供轨迹异常检测和分析的统一框架
"""

from .base_analyzer import BaseAnomalyAnalyzer, AnalysisResult
from .analysis_manager import AnomalyAnalysisManager
from .split_detector import SplitDetectionAnalyzer
from .id_switch_analyzer import IDSwitchAnalyzer
from .missing_gap_analyzer import MissingGapAnalyzer
from .result_summarizer import ResultSummarizer
from .accuracy_analyzer import AccuracyAnalyzer

# 导入扩展分析器（可选）
try:
    from .trajectory_quality_analyzer import TrajectoryQualityAnalyzer
    from .time_jump_analyzer import TimeJumpAnalyzer
    _extended_analyzers = ['TrajectoryQualityAnalyzer', 'TimeJumpAnalyzer']
except ImportError:
    _extended_analyzers = []

__all__ = [
    'BaseAnomalyAnalyzer',
    'AnalysisResult',
    'AnomalyAnalysisManager',
    'SplitDetectionAnalyzer',
    'IDSwitchAnalyzer',
    'MissingGapAnalyzer',
    'ResultSummarizer',
    'AccuracyAnalyzer'
] + _extended_analyzers

__version__ = '1.0.0'
