# 配置文件参考手册

## 📋 概述

本文档详细说明了车路协同轨迹匹配工具的配置参数，重点介绍统一评分系统的配置方法。

## 📁 配置文件结构

```
config/
├── unified_config.json     # 统一评分配置（推荐）
├── default.json           # 传统评分配置
└── framework_config.json  # 框架配置
```

## ⚙️ 统一评分配置详解

### 完整配置文件 (config/unified_config.json)

```json
{
  "description": "轨迹匹配系统统一配置文件",
  "version": "2.0",
  
  // ROI空间过滤配置
  "roi": {
    "roi_long": 20.0,                    // ROI纵向范围(米)
    "roi_lat": 5.0                       // ROI横向范围(米)
  },
  
  // 走廊过滤配置
  "corridor": {
    "enabled": true,                     // 启用走廊过滤
    "fallback_to_roi": true,             // 走廊失败时回退到ROI
    "downsample_interval_meters": 10.0,  // 下采样间隔(米)
    "long_buffer_meters": 25.0,          // 纵向缓冲区(米)
    "lat_buffer_meters": 5.0,            // 横向缓冲区(米)
    "time_buffer_seconds": 3600.0,       // 时间缓冲区(秒)
    "min_trajectory_points": 3,          // 最小轨迹点数
    "min_trajectory_duration": 0.5,      // 最小轨迹时长(秒)
    "direction_threshold_degrees": 60.0  // 方向阈值(度)
  },
  
  // 匹配算法配置
  "matching": {
    "win_sec": 3.0,                      // 时间窗口(秒)
    "local_match_thr": 0.7,              // 核心链匹配阈值
    "split_match_thr": 0.7,              // 分裂检测阈值
    "overlap_min": 0.5,                  // 最小重叠比例
    "max_gap": 2.0,                      // 最大间隙(秒)
    "gap_match_thr": 0.6,                // 间隙填充阈值
    "max_missing_gap": 5.0,              // 最大漏检间隙(秒)
    "min_missing_gap": 0.5,              // 最小漏检间隙(秒)
    "rtk_buffer": 2.0,                   // RTK缓冲区(秒)
    "good_match_thr": 0.6,               // 良好匹配阈值
    "min_segment_length": 2              // 最小轨迹段长度
  },

  // 统一评分系统配置 ⭐
  "scoring": {
    "method": "unified",                 // 评分方法: unified/legacy/f1_style
    
    // 传统评分权重（兼容性）
    "peak_weight": 0.6,                  // 峰值权重
    "duration_weight": 0.3,              // 时长权重
    "stability_weight": 0.1,             // 稳定性权重
    
    // F1评分权重（兼容性）
    "f1_weight": 0.8,                    // F1分数权重
    "direction_weight": 0.2,             // 方向一致性权重
    
    // 统一评分核心参数
    "spatial_decay_distance": 5.0,       // 空间衰减距离(米)
    "max_reasonable_duration": 20.0,     // 合理最大时长(秒)
    "direction_threshold_degrees": 45.0, // 方向阈值(度)
    "min_f1_threshold": 0.3,             // 最小F1阈值
    
    // 策略选择参数
    "peak_window_duration": 5.0,         // 峰值窗口时长(秒)
    "min_peak_threshold": 0.7,           // 最小峰值阈值
    "segment_duration": 5.0,             // 分段时长(秒)
    "quality_threshold_high": 0.8,       // 高质量阈值
    "quality_threshold_medium": 0.6,     // 中等质量阈值
    "short_trajectory_threshold": 10.0,  // 短轨迹阈值(秒)
    "sampling_rate": 10.0                // 采样率(Hz)
  },
  
  // 异常检测配置
  "anomaly": {
    "switch_dt": 2.0,                    // ID切换时间阈值(秒)
    "switch_dist": 10.0,                 // ID切换距离阈值(米)
    "switch_speed": 5.0,                 // ID切换速度阈值(m/s)
    "switch_heading": 30.0,              // ID切换方向阈值(度)
    "normal_detection_interval": 0.1,    // 正常检测间隔(秒)
    "tolerance_multiplier": 2.0          // 容忍度倍数
  },
  
  // 数据处理配置
  "processing": {
    "time_sync_enabled": true,           // 启用时间同步
    "spatial_filter_enabled": true,     // 启用空间过滤
    "data_validation_enabled": true,    // 启用数据验证
    "auto_format_detection": true       // 启用格式自动检测
  },
  
  // 输出配置
  "output": {
    "generate_csv": true,                // 生成CSV文件
    "generate_json": true,               // 生成JSON文件
    "generate_plots": false,             // 生成图表
    "include_debug_info": true           // 包含调试信息
  }
}
```

## 🎯 关键参数详解

### 统一评分核心参数

#### 1. 评分方法选择
```json
"method": "unified"    // 统一评分（推荐）
"method": "legacy"     // 传统评分
"method": "f1_style"   // F1评分
```

#### 2. 策略选择参数
```json
"short_trajectory_threshold": 10.0,    // 短轨迹阈值
"quality_threshold_high": 0.8,         // 高质量阈值
"quality_threshold_medium": 0.6        // 中等质量阈值
```

**调优建议**:
- `short_trajectory_threshold`: 5-15秒，基于实际ID切换频率
- `quality_threshold_high`: 0.7-0.9，影响峰值策略触发
- `quality_threshold_medium`: 0.5-0.7，影响分段策略

#### 3. 窗口和分段参数
```json
"peak_window_duration": 5.0,           // 峰值窗口时长
"segment_duration": 5.0                // 分段时长
```

**调优建议**:
- `peak_window_duration`: 3-10秒，平衡局部性和稳定性
- `segment_duration`: 与峰值窗口保持一致

#### 4. 空间质量参数
```json
"spatial_decay_distance": 5.0,         // 空间衰减距离
"direction_threshold_degrees": 45.0    // 方向阈值
```

**调优建议**:
- `spatial_decay_distance`: 3-10米，基于GPS精度
- `direction_threshold_degrees`: 30-60度，基于道路复杂度

### 匹配算法参数

#### 1. 核心阈值
```json
"local_match_thr": 0.7,                // 核心链匹配阈值
"split_match_thr": 0.7                 // 分裂检测阈值
```

**调优建议**:
- 高精度场景: 0.8-0.9
- 平衡场景: 0.7-0.8（推荐）
- 高召回场景: 0.6-0.7

#### 2. 时间窗口
```json
"win_sec": 3.0,                        // 匹配时间窗口
"max_gap": 2.0,                        // 最大间隙
"rtk_buffer": 2.0                      // RTK缓冲区
```

### 空间过滤参数

#### 1. ROI配置
```json
"roi_long": 20.0,                      // 纵向范围
"roi_lat": 5.0                         // 横向范围
```

#### 2. 走廊配置
```json
"long_buffer_meters": 25.0,            // 纵向缓冲
"lat_buffer_meters": 5.0               // 横向缓冲
```

## 📊 预设配置方案

### 方案1: 高精度匹配
```json
{
  "scoring": {
    "method": "unified",
    "local_match_thr": 0.8,
    "quality_threshold_high": 0.85,
    "short_trajectory_threshold": 8.0,
    "spatial_decay_distance": 4.0
  }
}
```

### 方案2: 平衡匹配（推荐）
```json
{
  "scoring": {
    "method": "unified",
    "local_match_thr": 0.7,
    "quality_threshold_high": 0.8,
    "short_trajectory_threshold": 10.0,
    "spatial_decay_distance": 5.0
  }
}
```

### 方案3: 高召回匹配
```json
{
  "scoring": {
    "method": "unified",
    "local_match_thr": 0.6,
    "quality_threshold_high": 0.7,
    "short_trajectory_threshold": 12.0,
    "spatial_decay_distance": 8.0
  }
}
```

### 方案4: 兼容传统
```json
{
  "scoring": {
    "method": "legacy",
    "local_match_thr": 0.8,
    "peak_weight": 0.6,
    "duration_weight": 0.3,
    "stability_weight": 0.1
  }
}
```

## 🔧 配置文件使用

### 1. 选择配置文件
```bash
# 使用统一评分配置
python main.py --config config/unified_config.json

# 使用传统配置
python main.py --config config/default.json

# 使用自定义配置
python main.py --config config/my_custom.json
```

### 2. 运行时参数覆盖
```bash
# 通过命令行参数覆盖配置
python main.py --config config/unified_config.json --threshold 0.8
```

### 3. 配置验证
```bash
# 验证配置文件格式
python -m json.tool config/unified_config.json

# 测试配置效果
python test_realistic_unified_scoring.py
```

## 📈 配置调优流程

### 1. 基线测试
```bash
# 使用默认配置建立基线
python main.py --config config/unified_config.json --rtk test_data/rtk.csv --perception test_data/perception.csv
```

### 2. 参数调优
```bash
# 调整关键参数
# 1. local_match_thr: 影响整体通过率
# 2. quality_threshold_high: 影响策略选择
# 3. short_trajectory_threshold: 影响短轨迹处理
```

### 3. 效果验证
```bash
# 对比调优前后效果
python test_realistic_unified_scoring.py
```

### 4. 生产部署
```bash
# 小规模验证
# 逐步扩大范围
# 监控关键指标
```

通过合理配置，统一评分系统可以适应各种应用场景，提供最优的轨迹匹配效果！
