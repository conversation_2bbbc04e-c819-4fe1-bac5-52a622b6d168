"""
精度分析器
对定位精度、速度精度、航向角精度进行量化分析
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import logging
import math
from bisect import bisect_left

from .base_analyzer import BaseAnomalyAnalyzer, AnalysisResult

try:
    from ..data_utils import RTKPoint, PerceptionPoint, GeoUtils
except ImportError:
    from core.data_utils import RTKPoint, PerceptionPoint, GeoUtils

logger = logging.getLogger(__name__)


class AccuracyAnalyzer(BaseAnomalyAnalyzer):
    """精度分析器 - 分析定位、速度、航向角精度"""
    
    def __init__(self, config: Dict[str, Any], name: str = None):
        super().__init__(config, name or 'AccuracyAnalyzer')
        
        # 提取精度分析相关配置
        self.interpolation_method = config.get('interpolation_method', 'linear')
        self.max_time_gap = config.get('max_time_gap', 2.0)  # 最大时间间隔(秒)
        
        # 精度阈值配置
        self.position_error_thresholds = config.get('position_error_thresholds', [1.0, 2.0, 5.0])
        self.speed_error_thresholds = config.get('speed_error_thresholds', [1.0, 3.0, 5.0])
        self.heading_error_thresholds = config.get('heading_error_thresholds', [5.0, 10.0, 30.0])
        
        # 统计指标配置
        self.statistical_metrics = config.get('statistical_metrics', ['mean', 'std', 'p50', 'p95', 'p99'])
        self.output_detailed_data = config.get('output_detailed_data', True)
        
        # 初始化地理工具
        self.geo_utils = GeoUtils()
        
        self.logger.info(f"精度分析器初始化完成，最大时间间隔: {self.max_time_gap}s")
    
    @property
    def analysis_type(self) -> str:
        return "accuracy"
    
    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        """
        执行精度分析
        
        Args:
            trajectory_chain: 轨迹链数据
            **kwargs: 其他参数，需要包含rtk_points
            
        Returns:
            AnalysisResult: 分析结果
        """
        self.log_analysis_start(trajectory_chain, **kwargs)
        
        if not self.validate_input(trajectory_chain, **kwargs):
            return self.create_result(False, "输入数据验证失败")
        
        # 检查必需的参数
        rtk_points = kwargs.get('rtk_points')
        if not rtk_points:
            return self.create_result(False, "缺少RTK轨迹数据")
        
        try:
            result = self.create_result()
            
            # 1. 提取公共时间段数据
            common_time_range = self._extract_common_time_range(trajectory_chain, rtk_points)
            if not common_time_range:
                return self.create_result(False, "没有找到公共时间段")
            
            # 2. 数据对齐和插值
            aligned_data = self._align_and_interpolate_data(trajectory_chain, rtk_points, common_time_range)
            if not aligned_data:
                return self.create_result(False, "数据对齐失败")
            
            # 3. 计算精度指标
            accuracy_metrics = self._calculate_accuracy_metrics(aligned_data)
            
            # 4. 统计分析
            statistical_summary = self._calculate_statistical_summary(accuracy_metrics)
            
            # 5. 构建分析结果
            result.metadata.update({
                'common_time_range': {
                    'start': common_time_range[0].isoformat(),
                    'end': common_time_range[1].isoformat(),
                    'duration': (common_time_range[1] - common_time_range[0]).total_seconds()
                },
                'samples_count': len(aligned_data),
                'statistical_summary': statistical_summary
            })
            
            # 添加详细数据（如果启用）
            if self.output_detailed_data:
                result.metadata['detailed_accuracy_data'] = accuracy_metrics
            
            # 添加精度事件
            self._add_accuracy_events(result, accuracy_metrics, statistical_summary)
            
            self.logger.info(f"精度分析完成，处理 {len(aligned_data)} 个数据点")
            
            return result
            
        except Exception as e:
            self.logger.error(f"精度分析过程中出错: {e}")
            return self.create_result(False, f"精度分析失败: {str(e)}")
    
    def _extract_common_time_range(self, trajectory_chain: List[Any], 
                                  rtk_points: List[RTKPoint]) -> Optional[Tuple[datetime, datetime]]:
        """
        提取感知轨迹和RTK数据的公共时间段
        
        Args:
            trajectory_chain: 轨迹链
            rtk_points: RTK轨迹点
            
        Returns:
            公共时间段的开始和结束时间
        """
        try:
            # 获取感知轨迹的时间范围
            perception_times = []
            for seg in trajectory_chain:
                if hasattr(seg, 'points') and seg.points:
                    for point in seg.points:
                        if hasattr(point, 'timestamp'):
                            perception_times.append(point.timestamp)
                        elif isinstance(point, dict) and 'timestamp' in point:
                            perception_times.append(point['timestamp'])
            
            if not perception_times:
                self.logger.error("感知轨迹中没有找到时间戳")
                return None
            
            perception_start = min(perception_times)
            perception_end = max(perception_times)
            
            # 获取RTK轨迹的时间范围
            if not rtk_points:
                self.logger.error("RTK轨迹为空")
                return None
            
            rtk_start = min(point.timestamp for point in rtk_points)
            rtk_end = max(point.timestamp for point in rtk_points)
            
            # 计算公共时间段
            common_start = max(perception_start, rtk_start)
            common_end = min(perception_end, rtk_end)
            
            if common_start >= common_end:
                self.logger.error(f"没有公共时间段: 感知({perception_start}-{perception_end}), RTK({rtk_start}-{rtk_end})")
                return None
            
            self.logger.info(f"公共时间段: {common_start} - {common_end}, 时长: {(common_end - common_start).total_seconds():.1f}s")
            
            return (common_start, common_end)
            
        except Exception as e:
            self.logger.error(f"提取公共时间段失败: {e}")
            return None
    
    def _align_and_interpolate_data(self, trajectory_chain: List[Any], 
                                   rtk_points: List[RTKPoint],
                                   common_time_range: Tuple[datetime, datetime]) -> List[Dict[str, Any]]:
        """
        对齐感知轨迹和RTK数据，并进行插值
        
        Args:
            trajectory_chain: 轨迹链
            rtk_points: RTK轨迹点
            common_time_range: 公共时间段
            
        Returns:
            对齐后的数据列表
        """
        try:
            aligned_data = []
            common_start, common_end = common_time_range
            
            # 预处理RTK数据，按时间排序并创建时间索引
            sorted_rtk = sorted(rtk_points, key=lambda x: x.timestamp)
            rtk_timestamps = [point.timestamp for point in sorted_rtk]
            
            # 遍历感知轨迹中的每个点
            for seg in trajectory_chain:
                if not hasattr(seg, 'points') or not seg.points:
                    continue
                
                for point in seg.points:
                    # 提取感知点数据
                    if hasattr(point, 'timestamp'):  # PerceptionPoint对象
                        per_timestamp = point.timestamp
                        per_data = {
                            'id': seg.id,
                            'lat': point.lat,
                            'lon': point.lon,
                            'speed': point.speed,
                            'heading': point.heading
                        }
                    elif isinstance(point, dict):  # 字典形式
                        per_timestamp = point.get('timestamp')
                        per_data = {
                            'id': seg.id,
                            'lat': point.get('lat'),
                            'lon': point.get('lon'),
                            'speed': point.get('speed'),
                            'heading': point.get('heading')
                        }
                    else:
                        continue
                    
                    # 检查是否在公共时间段内
                    if not (common_start <= per_timestamp <= common_end):
                        continue
                    
                    # 插值获取对应时刻的RTK数据
                    rtk_interpolated = self._interpolate_rtk_at_timestamp(
                        per_timestamp, sorted_rtk, rtk_timestamps
                    )
                    
                    if rtk_interpolated:
                        aligned_data.append({
                            'timestamp': per_timestamp,
                            'perception': per_data,
                            'rtk_interpolated': rtk_interpolated
                        })
            
            self.logger.info(f"数据对齐完成，共 {len(aligned_data)} 个有效数据点")
            
            return aligned_data
            
        except Exception as e:
            self.logger.error(f"数据对齐失败: {e}")
            return []

    def _interpolate_rtk_at_timestamp(self, target_time: datetime,
                                     sorted_rtk: List[RTKPoint],
                                     rtk_timestamps: List[datetime]) -> Optional[Dict[str, Any]]:
        """
        基于目标时间戳，在RTK轨迹中进行线性插值

        Args:
            target_time: 目标时间戳
            sorted_rtk: 按时间排序的RTK点列表
            rtk_timestamps: RTK时间戳列表

        Returns:
            插值得到的RTK数据
        """
        try:
            # 使用二分查找找到插入位置
            insert_pos = bisect_left(rtk_timestamps, target_time)

            # 处理边界情况
            if insert_pos == 0:
                # 目标时间在第一个RTK点之前
                if len(sorted_rtk) > 1:
                    time_diff = (target_time - rtk_timestamps[0]).total_seconds()
                    if abs(time_diff) <= self.max_time_gap:
                        # 使用前两个点进行外推
                        return self._linear_interpolate(
                            sorted_rtk[0], sorted_rtk[1], target_time
                        )
                return None

            elif insert_pos >= len(sorted_rtk):
                # 目标时间在最后一个RTK点之后
                if len(sorted_rtk) > 1:
                    time_diff = (target_time - rtk_timestamps[-1]).total_seconds()
                    if abs(time_diff) <= self.max_time_gap:
                        # 使用最后两个点进行外推
                        return self._linear_interpolate(
                            sorted_rtk[-2], sorted_rtk[-1], target_time
                        )
                return None

            else:
                # 目标时间在两个RTK点之间
                rtk_before = sorted_rtk[insert_pos - 1]
                rtk_after = sorted_rtk[insert_pos]

                # 检查时间间隔
                time_gap = (rtk_after.timestamp - rtk_before.timestamp).total_seconds()
                if time_gap > self.max_time_gap:
                    self.logger.debug(f"RTK点间隔过大: {time_gap:.2f}s > {self.max_time_gap}s")
                    return None

                # 进行线性插值
                return self._linear_interpolate(rtk_before, rtk_after, target_time)

        except Exception as e:
            self.logger.error(f"RTK插值失败: {e}")
            return None

    def _linear_interpolate(self, rtk_before: RTKPoint, rtk_after: RTKPoint,
                           target_time: datetime) -> Dict[str, Any]:
        """
        在两个RTK点之间进行线性插值

        Args:
            rtk_before: 前一个RTK点
            rtk_after: 后一个RTK点
            target_time: 目标时间

        Returns:
            插值结果
        """
        try:
            # 计算时间比例
            total_time = (rtk_after.timestamp - rtk_before.timestamp).total_seconds()
            if total_time == 0:
                # 时间相同，返回前一个点的数据
                return {
                    'lat': rtk_before.lat,
                    'lon': rtk_before.lon,
                    'speed': rtk_before.speed,
                    'heading': rtk_before.heading,
                    'interpolation_method': 'exact'
                }

            elapsed_time = (target_time - rtk_before.timestamp).total_seconds()
            ratio = elapsed_time / total_time

            # 线性插值位置
            lat_interpolated = rtk_before.lat + (rtk_after.lat - rtk_before.lat) * ratio
            lon_interpolated = rtk_before.lon + (rtk_after.lon - rtk_before.lon) * ratio

            # 线性插值速度
            speed_interpolated = rtk_before.speed + (rtk_after.speed - rtk_before.speed) * ratio

            # 航向角插值（考虑360°环绕）
            heading_interpolated = self._interpolate_heading(
                rtk_before.heading, rtk_after.heading, ratio
            )

            return {
                'lat': lat_interpolated,
                'lon': lon_interpolated,
                'speed': speed_interpolated,
                'heading': heading_interpolated,
                'interpolation_method': 'linear',
                'interpolation_ratio': ratio
            }

        except Exception as e:
            self.logger.error(f"线性插值计算失败: {e}")
            return None

    def _interpolate_heading(self, heading_before: float, heading_after: float, ratio: float) -> float:
        """
        航向角插值，考虑360°环绕问题

        Args:
            heading_before: 前一个航向角
            heading_after: 后一个航向角
            ratio: 插值比例

        Returns:
            插值后的航向角
        """
        try:
            # 将航向角标准化到[0, 360)范围
            h1 = heading_before % 360
            h2 = heading_after % 360

            # 计算角度差
            diff = h2 - h1

            # 处理跨越0°的情况
            if diff > 180:
                diff -= 360
            elif diff < -180:
                diff += 360

            # 线性插值
            interpolated = h1 + diff * ratio

            # 标准化到[0, 360)范围
            return interpolated % 360

        except Exception as e:
            self.logger.error(f"航向角插值失败: {e}")
            return heading_before

    def _calculate_accuracy_metrics(self, aligned_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        计算精度指标

        Args:
            aligned_data: 对齐后的数据

        Returns:
            精度指标列表
        """
        accuracy_metrics = []

        try:
            for data_point in aligned_data:
                perception = data_point['perception']
                rtk_interpolated = data_point['rtk_interpolated']
                timestamp = data_point['timestamp']

                # 计算定位精度（地理距离误差）
                position_error = self.geo_utils.haversine_distance(
                    rtk_interpolated['lat'], rtk_interpolated['lon'],
                    perception['lat'], perception['lon']
                )

                # 计算速度精度
                speed_abs_error = abs(rtk_interpolated['speed'] - perception['speed'])
                speed_rel_error = (speed_abs_error / max(rtk_interpolated['speed'], 0.1)) * 100

                # 计算航向角精度（考虑360°环绕）
                heading_error = self._calculate_heading_error(
                    rtk_interpolated['heading'], perception['heading']
                )

                # 构建精度指标
                accuracy_metric = {
                    'timestamp': timestamp.isoformat(),
                    'perception_id': perception['id'],
                    'position_error': round(position_error, 3),
                    'speed_abs_error': round(speed_abs_error, 3),
                    'speed_rel_error': round(speed_rel_error, 2),
                    'heading_error': round(heading_error, 2),
                    'rtk_interpolated': {
                        'lat': round(rtk_interpolated['lat'], 8),
                        'lon': round(rtk_interpolated['lon'], 8),
                        'speed': round(rtk_interpolated['speed'], 2),
                        'heading': round(rtk_interpolated['heading'], 2),
                        'method': rtk_interpolated.get('interpolation_method', 'unknown')
                    },
                    'perception_data': {
                        'lat': round(perception['lat'], 8),
                        'lon': round(perception['lon'], 8),
                        'speed': round(perception['speed'], 2),
                        'heading': round(perception['heading'], 2)
                    }
                }

                accuracy_metrics.append(accuracy_metric)

            self.logger.info(f"精度指标计算完成，共 {len(accuracy_metrics)} 个数据点")

            return accuracy_metrics

        except Exception as e:
            self.logger.error(f"精度指标计算失败: {e}")
            return []

    def _calculate_heading_error(self, rtk_heading: float, perception_heading: float) -> float:
        """
        计算航向角误差，考虑360°环绕

        Args:
            rtk_heading: RTK航向角
            perception_heading: 感知航向角

        Returns:
            航向角误差（度）
        """
        try:
            # 标准化到[0, 360)范围
            h1 = rtk_heading % 360
            h2 = perception_heading % 360

            # 计算最小角度差
            diff = abs(h1 - h2)
            return min(diff, 360 - diff)

        except Exception as e:
            self.logger.error(f"航向角误差计算失败: {e}")
            return 0.0

    def _calculate_statistical_summary(self, accuracy_metrics: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算统计汇总

        Args:
            accuracy_metrics: 精度指标列表

        Returns:
            统计汇总结果
        """
        try:
            if not accuracy_metrics:
                return {}

            # 提取各项误差数据
            position_errors = [m['position_error'] for m in accuracy_metrics]
            speed_abs_errors = [m['speed_abs_error'] for m in accuracy_metrics]
            speed_rel_errors = [m['speed_rel_error'] for m in accuracy_metrics]
            heading_errors = [m['heading_error'] for m in accuracy_metrics]

            # 计算统计指标
            summary = {
                'position_accuracy': self._calculate_error_statistics(position_errors, 'position'),
                'speed_accuracy': {
                    'absolute': self._calculate_error_statistics(speed_abs_errors, 'speed_abs'),
                    'relative': self._calculate_error_statistics(speed_rel_errors, 'speed_rel')
                },
                'heading_accuracy': self._calculate_error_statistics(heading_errors, 'heading'),
                'overall_statistics': {
                    'total_samples': len(accuracy_metrics),
                    'analysis_duration': self._calculate_analysis_duration(accuracy_metrics)
                }
            }

            # 添加精度等级分布
            summary['accuracy_distribution'] = {
                'position': self._calculate_accuracy_distribution(position_errors, self.position_error_thresholds),
                'speed': self._calculate_accuracy_distribution(speed_abs_errors, self.speed_error_thresholds),
                'heading': self._calculate_accuracy_distribution(heading_errors, self.heading_error_thresholds)
            }

            # 添加时间维度分析
            summary['time_analysis'] = self._calculate_time_dimension_analysis(accuracy_metrics)

            return summary

        except Exception as e:
            self.logger.error(f"统计汇总计算失败: {e}")
            return {}

    def _calculate_error_statistics(self, errors: List[float], error_type: str) -> Dict[str, float]:
        """
        计算误差统计指标

        Args:
            errors: 误差列表
            error_type: 误差类型

        Returns:
            统计指标字典
        """
        try:
            if not errors:
                return {}

            errors_array = np.array(errors)

            stats = {
                'count': len(errors),
                'mean': float(np.mean(errors_array)),
                'std': float(np.std(errors_array)),
                'min': float(np.min(errors_array)),
                'max': float(np.max(errors_array))
            }

            # 添加分位数
            percentiles = [25, 50, 75, 90, 95, 99]
            for p in percentiles:
                stats[f'p{p}'] = float(np.percentile(errors_array, p))

            # 四舍五入
            for key, value in stats.items():
                if isinstance(value, float):
                    if error_type == 'speed_rel':
                        stats[key] = round(value, 2)  # 相对误差保留2位小数
                    else:
                        stats[key] = round(value, 3)  # 其他误差保留3位小数

            return stats

        except Exception as e:
            self.logger.error(f"误差统计计算失败: {e}")
            return {}

    def _calculate_accuracy_distribution(self, errors: List[float], thresholds: List[float]) -> Dict[str, Any]:
        """
        计算精度等级分布

        Args:
            errors: 误差列表
            thresholds: 精度阈值列表

        Returns:
            精度分布统计
        """
        try:
            if not errors or not thresholds:
                return {}

            total_count = len(errors)
            distribution = {}

            # 计算各等级的数量和比例
            for i, threshold in enumerate(thresholds):
                count = sum(1 for error in errors if error <= threshold)
                percentage = (count / total_count) * 100

                level_name = f"level_{i+1}_le_{threshold}"
                distribution[level_name] = {
                    'threshold': threshold,
                    'count': count,
                    'percentage': round(percentage, 2)
                }

            # 计算超出最大阈值的数量
            max_threshold = max(thresholds)
            exceed_count = sum(1 for error in errors if error > max_threshold)
            exceed_percentage = (exceed_count / total_count) * 100

            distribution['exceed_max_threshold'] = {
                'threshold': max_threshold,
                'count': exceed_count,
                'percentage': round(exceed_percentage, 2)
            }

            return distribution

        except Exception as e:
            self.logger.error(f"精度分布计算失败: {e}")
            return {}

    def _calculate_time_dimension_analysis(self, accuracy_metrics: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算时间维度分析

        Args:
            accuracy_metrics: 精度指标列表

        Returns:
            时间维度分析结果
        """
        try:
            if not accuracy_metrics:
                return {}

            from datetime import datetime, timedelta
            import statistics

            time_analysis = {
                'hourly_stats': {},
                'trend_analysis': {},
                'peak_error_periods': [],
                'stability_metrics': {}
            }

            # 按小时分组统计
            hourly_groups = {}
            timestamps = []
            position_errors = []
            speed_errors = []
            heading_errors = []

            for metric in accuracy_metrics:
                timestamp_str = metric.get('timestamp', '')
                if not timestamp_str:
                    continue

                try:
                    dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    hour_key = dt.hour

                    if hour_key not in hourly_groups:
                        hourly_groups[hour_key] = {
                            'position_errors': [],
                            'speed_errors': [],
                            'heading_errors': [],
                            'count': 0
                        }

                    hourly_groups[hour_key]['position_errors'].append(metric.get('position_error', 0))
                    hourly_groups[hour_key]['speed_errors'].append(metric.get('speed_abs_error', 0))
                    hourly_groups[hour_key]['heading_errors'].append(metric.get('heading_error', 0))
                    hourly_groups[hour_key]['count'] += 1

                    # 收集时间序列数据用于趋势分析
                    timestamps.append(dt)
                    position_errors.append(metric.get('position_error', 0))
                    speed_errors.append(metric.get('speed_abs_error', 0))
                    heading_errors.append(metric.get('heading_error', 0))

                except Exception as e:
                    continue

            # 计算每小时统计
            for hour, data in hourly_groups.items():
                if data['position_errors']:
                    time_analysis['hourly_stats'][f'{hour:02d}:00'] = {
                        'count': data['count'],
                        'position_mean': round(statistics.mean(data['position_errors']), 3),
                        'position_std': round(statistics.stdev(data['position_errors']) if len(data['position_errors']) > 1 else 0, 3),
                        'speed_mean': round(statistics.mean(data['speed_errors']), 3),
                        'heading_mean': round(statistics.mean(data['heading_errors']), 2)
                    }

            # 趋势分析
            if len(position_errors) > 1:
                time_analysis['trend_analysis'] = {
                    'position_trend': self._calculate_trend(position_errors),
                    'speed_trend': self._calculate_trend(speed_errors),
                    'heading_trend': self._calculate_trend(heading_errors)
                }

            # 识别高误差时段
            time_analysis['peak_error_periods'] = self._identify_peak_error_periods(accuracy_metrics)

            # 稳定性指标
            if position_errors:
                time_analysis['stability_metrics'] = {
                    'position_cv': round((statistics.stdev(position_errors) / statistics.mean(position_errors)) * 100, 2) if statistics.mean(position_errors) > 0 else 0,
                    'speed_cv': round((statistics.stdev(speed_errors) / statistics.mean(speed_errors)) * 100, 2) if statistics.mean(speed_errors) > 0 else 0,
                    'heading_cv': round((statistics.stdev(heading_errors) / statistics.mean(heading_errors)) * 100, 2) if statistics.mean(heading_errors) > 0 else 0
                }

            self.logger.info(f"时间维度分析完成，覆盖 {len(hourly_groups)} 个小时")
            return time_analysis

        except Exception as e:
            self.logger.error(f"时间维度分析失败: {e}")
            return {}

    def _calculate_trend(self, values: List[float]) -> str:
        """
        计算趋势方向
        """
        try:
            if len(values) < 2:
                return 'stable'

            # 简单线性趋势计算
            n = len(values)
            x = list(range(n))

            # 计算斜率
            x_mean = sum(x) / n
            y_mean = sum(values) / n

            numerator = sum((x[i] - x_mean) * (values[i] - y_mean) for i in range(n))
            denominator = sum((x[i] - x_mean) ** 2 for i in range(n))

            if denominator == 0:
                return 'stable'

            slope = numerator / denominator

            if slope > 0.01:
                return 'increasing'
            elif slope < -0.01:
                return 'decreasing'
            else:
                return 'stable'

        except Exception:
            return 'unknown'

    def _identify_peak_error_periods(self, accuracy_metrics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        识别高误差时段
        """
        try:
            peak_periods = []

            # 定义高误差阈值
            high_position_threshold = max(self.position_error_thresholds)
            high_speed_threshold = max(self.speed_error_thresholds)
            high_heading_threshold = max(self.heading_error_thresholds)

            for metric in accuracy_metrics:
                timestamp = metric.get('timestamp', '')
                position_error = metric.get('position_error', 0)
                speed_error = metric.get('speed_abs_error', 0)
                heading_error = metric.get('heading_error', 0)

                # 检查是否为高误差
                if (position_error > high_position_threshold or
                    speed_error > high_speed_threshold or
                    heading_error > high_heading_threshold):

                    peak_periods.append({
                        'timestamp': timestamp,
                        'position_error': position_error,
                        'speed_error': speed_error,
                        'heading_error': heading_error,
                        'severity': 'high' if position_error > high_position_threshold * 2 else 'medium'
                    })

            # 按时间排序并限制数量
            peak_periods.sort(key=lambda x: x['timestamp'])
            return peak_periods[:20]  # 最多返回20个高误差时段

        except Exception as e:
            self.logger.error(f"识别高误差时段失败: {e}")
            return []

    def _calculate_analysis_duration(self, accuracy_metrics: List[Dict[str, Any]]) -> float:
        """
        计算分析时长

        Args:
            accuracy_metrics: 精度指标列表

        Returns:
            分析时长（秒）
        """
        try:
            if len(accuracy_metrics) < 2:
                return 0.0

            timestamps = [datetime.fromisoformat(m['timestamp']) for m in accuracy_metrics]
            start_time = min(timestamps)
            end_time = max(timestamps)

            return (end_time - start_time).total_seconds()

        except Exception as e:
            self.logger.error(f"分析时长计算失败: {e}")
            return 0.0

    def _add_accuracy_events(self, result: AnalysisResult,
                           accuracy_metrics: List[Dict[str, Any]],
                           statistical_summary: Dict[str, Any]):
        """
        添加精度相关事件

        Args:
            result: 分析结果对象
            accuracy_metrics: 精度指标列表
            statistical_summary: 统计汇总
        """
        try:
            # 添加精度汇总事件
            if statistical_summary:
                summary_event = {
                    'event_type': 'accuracy_summary',
                    'timestamp': datetime.now().isoformat(),
                    'description': '精度分析汇总',
                    'details': {
                        'position_mean_error': statistical_summary.get('position_accuracy', {}).get('mean', 0),
                        'speed_mean_error': statistical_summary.get('speed_accuracy', {}).get('absolute', {}).get('mean', 0),
                        'heading_mean_error': statistical_summary.get('heading_accuracy', {}).get('mean', 0),
                        'total_samples': len(accuracy_metrics)
                    }
                }
                result.add_event(summary_event)

            # 添加高误差事件
            self._add_high_error_events(result, accuracy_metrics)

        except Exception as e:
            self.logger.error(f"添加精度事件失败: {e}")

    def _add_high_error_events(self, result: AnalysisResult, accuracy_metrics: List[Dict[str, Any]]):
        """
        添加高误差事件

        Args:
            result: 分析结果对象
            accuracy_metrics: 精度指标列表
        """
        try:
            # 定义高误差阈值（使用配置的最大阈值）
            high_position_threshold = max(self.position_error_thresholds) if self.position_error_thresholds else 5.0
            high_speed_threshold = max(self.speed_error_thresholds) if self.speed_error_thresholds else 5.0
            high_heading_threshold = max(self.heading_error_thresholds) if self.heading_error_thresholds else 30.0

            for metric in accuracy_metrics:
                events = []

                # 检查定位高误差
                if metric['position_error'] > high_position_threshold:
                    events.append({
                        'event_type': 'high_position_error',
                        'timestamp': metric['timestamp'],
                        'description': f'定位误差过大: {metric["position_error"]:.3f}m',
                        'details': {
                            'error_value': metric['position_error'],
                            'threshold': high_position_threshold,
                            'perception_id': metric['perception_id']
                        }
                    })

                # 检查速度高误差
                if metric['speed_abs_error'] > high_speed_threshold:
                    events.append({
                        'event_type': 'high_speed_error',
                        'timestamp': metric['timestamp'],
                        'description': f'速度误差过大: {metric["speed_abs_error"]:.3f}m/s',
                        'details': {
                            'abs_error': metric['speed_abs_error'],
                            'rel_error': metric['speed_rel_error'],
                            'threshold': high_speed_threshold,
                            'perception_id': metric['perception_id']
                        }
                    })

                # 检查航向角高误差
                if metric['heading_error'] > high_heading_threshold:
                    events.append({
                        'event_type': 'high_heading_error',
                        'timestamp': metric['timestamp'],
                        'description': f'航向角误差过大: {metric["heading_error"]:.2f}°',
                        'details': {
                            'error_value': metric['heading_error'],
                            'threshold': high_heading_threshold,
                            'perception_id': metric['perception_id']
                        }
                    })

                # 添加事件到结果
                for event in events:
                    result.add_event(event)

        except Exception as e:
            self.logger.error(f"添加高误差事件失败: {e}")

    def validate_input(self, trajectory_chain: List[Any], **kwargs) -> bool:
        """
        验证输入数据

        Args:
            trajectory_chain: 轨迹链数据
            **kwargs: 其他参数

        Returns:
            验证结果
        """
        try:
            # 检查轨迹链
            if not trajectory_chain:
                self.logger.error("轨迹链为空")
                return False

            # 检查轨迹链中是否有有效数据
            valid_segments = 0
            for seg in trajectory_chain:
                if hasattr(seg, 'points') and seg.points:
                    valid_segments += 1

            if valid_segments == 0:
                self.logger.error("轨迹链中没有有效的轨迹段")
                return False

            # 检查RTK数据
            rtk_points = kwargs.get('rtk_points')
            if not rtk_points:
                self.logger.error("缺少RTK轨迹数据")
                return False

            if len(rtk_points) < 2:
                self.logger.error("RTK轨迹点数量不足，至少需要2个点进行插值")
                return False

            self.logger.info(f"输入验证通过: {valid_segments}个有效轨迹段, {len(rtk_points)}个RTK点")
            return True

        except Exception as e:
            self.logger.error(f"输入验证失败: {e}")
            return False
