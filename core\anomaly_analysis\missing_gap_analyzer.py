"""
漏检识别分析器
使用增强间隙分析器识别缺失的轨迹段
"""

from typing import List, Dict, Any, Optional
import logging
from datetime import datetime

from .base_analyzer import BaseAnomalyAnalyzer, AnalysisResult

logger = logging.getLogger(__name__)


class MissingGapAnalyzer(BaseAnomalyAnalyzer):
    """漏检识别分析器"""
    
    def __init__(self, config: Dict[str, Any], name: str = None):
        super().__init__(config, name or 'MissingGapAnalyzer')
        
        # 提取漏检识别相关配置
        self.min_missing_gap = config.get('min_missing_gap', 0.5)  # 最小漏检间隙(秒)
        self.max_missing_gap = config.get('max_missing_gap', 5.0)  # 最大漏检间隙(秒)
        self.normal_detection_interval = config.get('normal_detection_interval', 0.1)  # 正常检测间隔(秒)
        
        # 运动连续性阈值
        self.switch_dt = config.get('switch_dt', 2.0)
        self.switch_dist = config.get('switch_dist', 10.0)
        self.switch_speed = config.get('switch_speed', 5.0)
        self.switch_heading = config.get('switch_heading', 30.0)
        self.tolerance_multiplier = config.get('tolerance_multiplier', 2.0)
        
        # 初始化增强间隙分析器
        self._init_enhanced_analyzer()
        
        self.logger.info(f"漏检识别分析器初始化: 最小间隙={self.min_missing_gap}s, "
                        f"最大间隙={self.max_missing_gap}s")
    
    def _init_enhanced_analyzer(self):
        """初始化增强间隙分析器"""
        try:
            from ..gap_analyzer import EnhancedGapAnalyzer
            
            # 构建配置字典
            gap_config = {
                'min_missing_gap': self.min_missing_gap,
                'max_missing_gap': self.max_missing_gap,
                'switch_dt': self.switch_dt,
                'switch_dist': self.switch_dist,
                'switch_speed': self.switch_speed,
                'switch_heading': self.switch_heading,
                'normal_detection_interval': self.normal_detection_interval,
                'tolerance_multiplier': self.tolerance_multiplier
            }
            
            self.enhanced_analyzer = EnhancedGapAnalyzer(gap_config)
            self.logger.info("增强间隙分析器初始化成功")
            
        except ImportError as e:
            self.logger.error(f"无法导入增强间隙分析器: {e}")
            self.enhanced_analyzer = None
    
    @property
    def analysis_type(self) -> str:
        return "missing_gap"
    
    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        """
        执行漏检识别分析
        
        Args:
            trajectory_chain: 轨迹链数据
            **kwargs: 其他参数，需要包含rtk_start_time和rtk_end_time
            
        Returns:
            AnalysisResult: 分析结果
        """
        self.log_analysis_start(trajectory_chain, **kwargs)
        
        if not self.validate_input(trajectory_chain, **kwargs):
            return self.create_result(False, "输入数据验证失败")
        
        # 检查必需的参数
        rtk_start_time = kwargs.get('rtk_start_time')
        rtk_end_time = kwargs.get('rtk_end_time')
        
        if not rtk_start_time or not rtk_end_time:
            return self.create_result(False, "缺少RTK时间范围参数")
        
        if not self.enhanced_analyzer:
            return self.create_result(False, "增强间隙分析器不可用")
        
        try:
            result = self.create_result()
            
            # 执行漏检识别
            missing_gaps = self._identify_missing_gaps(trajectory_chain, rtk_start_time, rtk_end_time)
            
            # 添加事件到结果
            for gap in missing_gaps:
                result.add_event(gap)
            
            # 添加统计信息
            result.add_statistic('total_missing_gaps', len(missing_gaps))
            result.add_statistic('trajectory_segments', len(trajectory_chain))
            
            # 按漏检类型统计
            gap_types = {}
            total_missing_duration = 0
            
            for gap in missing_gaps:
                gap_type = gap.get('type', 'unknown')
                gap_types[gap_type] = gap_types.get(gap_type, 0) + 1
                total_missing_duration += gap.get('duration', 0)
            
            result.add_statistic('gap_types', gap_types)
            result.add_statistic('total_missing_duration', total_missing_duration)
            
            # RTK覆盖率统计
            rtk_total_duration = (rtk_end_time - rtk_start_time).total_seconds()
            coverage_ratio = 1.0 - (total_missing_duration / rtk_total_duration) if rtk_total_duration > 0 else 0
            result.add_statistic('rtk_coverage_ratio', coverage_ratio)
            
            # 添加元数据
            result.add_metadata('analysis_method', 'enhanced_gap_analysis')
            result.add_metadata('min_missing_gap', self.min_missing_gap)
            result.add_metadata('max_missing_gap', self.max_missing_gap)
            result.add_metadata('rtk_time_range', {
                'start': rtk_start_time.isoformat(),
                'end': rtk_end_time.isoformat(),
                'duration': rtk_total_duration
            })
            
            # 获取增强分析结果
            if hasattr(self.enhanced_analyzer, 'analysis_results'):
                result.add_metadata('enhanced_analysis', self.enhanced_analyzer.analysis_results)
            
            self.log_analysis_complete(result)
            return result
            
        except Exception as e:
            error_msg = f"漏检识别分析失败: {str(e)}"
            self.logger.error(error_msg)
            return self.create_result(False, error_msg)
    
    def _identify_missing_gaps(self, trajectory_chain: List[Any], 
                              rtk_start_time: datetime, rtk_end_time: datetime) -> List[Dict[str, Any]]:
        """
        识别漏检间隙
        
        Args:
            trajectory_chain: 轨迹链
            rtk_start_time: RTK开始时间
            rtk_end_time: RTK结束时间
            
        Returns:
            List[Dict[str, Any]]: 漏检间隙列表
        """
        if not self.enhanced_analyzer:
            self.logger.error("增强间隙分析器不可用")
            return []
        
        try:
            # 使用增强间隙分析器进行漏检识别
            missing_gaps = self.enhanced_analyzer.identify_missing_gaps(
                trajectory_chain, rtk_start_time, rtk_end_time
            )
            
            # 为每个间隙添加分析器信息
            for gap in missing_gaps:
                gap['analyzer'] = self.name
                gap['analysis_type'] = self.analysis_type
            
            self.logger.info(f"漏检识别完成，发现 {len(missing_gaps)} 个漏检间隙")
            
            return missing_gaps
            
        except Exception as e:
            self.logger.error(f"漏检识别过程中出错: {e}")
            return []
    
    def analyze_trajectory_internal_jumps(self, matched_data) -> List[Dict[str, Any]]:
        """
        分析轨迹内部时间跳跃
        
        Args:
            matched_data: 匹配数据DataFrame
            
        Returns:
            List[Dict[str, Any]]: 内部跳跃事件列表
        """
        if not self.enhanced_analyzer:
            self.logger.warning("增强间隙分析器不可用，无法分析内部跳跃")
            return []
        
        try:
            return self.enhanced_analyzer.analyze_trajectory_internal_jumps(matched_data)
        except Exception as e:
            self.logger.error(f"内部跳跃分析失败: {e}")
            return []
    
    def get_enhanced_analysis_results(self) -> Optional[Dict[str, Any]]:
        """获取增强分析结果"""
        if self.enhanced_analyzer and hasattr(self.enhanced_analyzer, 'analysis_results'):
            return self.enhanced_analyzer.analysis_results
        return None
    
    def generate_gap_report(self) -> Optional[Dict[str, Any]]:
        """生成间隙分析报告"""
        if not self.enhanced_analyzer:
            return None
        
        try:
            return self.enhanced_analyzer.generate_enhanced_gap_report()
        except Exception as e:
            self.logger.error(f"生成间隙报告失败: {e}")
            return None
    
    def print_analysis_summary(self):
        """打印分析摘要"""
        if self.enhanced_analyzer and hasattr(self.enhanced_analyzer, 'print_enhanced_analysis_summary'):
            try:
                self.enhanced_analyzer.print_enhanced_analysis_summary()
            except Exception as e:
                self.logger.error(f"打印分析摘要失败: {e}")
        else:
            self.logger.warning("无法打印分析摘要：增强分析器不可用")
    
    def validate_input(self, trajectory_chain: List[Any], **kwargs) -> bool:
        """验证输入数据"""
        if not super().validate_input(trajectory_chain, **kwargs):
            return False
        
        # 检查RTK时间参数
        rtk_start_time = kwargs.get('rtk_start_time')
        rtk_end_time = kwargs.get('rtk_end_time')
        
        if not rtk_start_time or not rtk_end_time:
            self.logger.error("缺少RTK时间范围参数")
            return False
        
        if rtk_start_time >= rtk_end_time:
            self.logger.error("RTK时间范围无效")
            return False
        
        return True
